{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "lint": {"outputs": []}, "clean": {"cache": false}, "db:migrate": {"cache": false}, "db:seed": {"cache": false}, "type-check": {"dependsOn": ["^build"], "outputs": []}}}