#!/bin/bash

# Verification Script for Trading Platform Setup
# This script verifies that the development environment is properly configured

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# Check Node.js version
check_nodejs() {
    print_status "Checking Node.js version..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        return 1
    fi
    
    NODE_VERSION=$(node -v)
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d'v' -f2 | cut -d'.' -f1)
    
    if [ "$NODE_MAJOR" -ge 18 ]; then
        print_success "Node.js version: $NODE_VERSION"
    else
        print_error "Node.js version $NODE_VERSION is too old. Requires 18+"
        return 1
    fi
}

# Check npm version
check_npm() {
    print_status "Checking npm version..."
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        return 1
    fi
    
    NPM_VERSION=$(npm -v)
    NPM_MAJOR=$(echo $NPM_VERSION | cut -d'.' -f1)
    
    if [ "$NPM_MAJOR" -ge 9 ]; then
        print_success "npm version: $NPM_VERSION"
    else
        print_warning "npm version $NPM_VERSION is old. Recommended: 9+"
    fi
}

# Check package manager configuration
check_package_manager() {
    print_status "Checking package manager configuration..."
    
    if grep -q '"packageManager"' package.json; then
        PACKAGE_MANAGER=$(grep '"packageManager"' package.json | cut -d'"' -f4)
        print_success "Package manager configured: $PACKAGE_MANAGER"
    else
        print_error "Package manager not specified in package.json"
        return 1
    fi
}

# Check workspace configuration
check_workspaces() {
    print_status "Checking workspace configuration..."
    
    if grep -q '"workspaces"' package.json; then
        print_success "Workspaces configured"
    else
        print_error "Workspaces not configured"
        return 1
    fi
    
    # Check if workspace directories exist
    for workspace in apps/backend apps/frontend packages/shared; do
        if [ -d "$workspace" ]; then
            print_success "Workspace exists: $workspace"
        else
            print_error "Workspace missing: $workspace"
            return 1
        fi
    done
}

# Check dependencies
check_dependencies() {
    print_status "Checking dependencies..."
    
    if [ -f "package-lock.json" ]; then
        print_success "package-lock.json exists"
    else
        print_warning "package-lock.json not found. Run 'npm install'"
    fi
    
    if [ -d "node_modules" ]; then
        print_success "node_modules directory exists"
    else
        print_error "node_modules not found. Run 'npm install'"
        return 1
    fi
}

# Check Docker
check_docker() {
    print_status "Checking Docker..."
    
    if ! command -v docker &> /dev/null; then
        print_warning "Docker is not installed"
        return 0
    fi
    
    if docker ps &> /dev/null; then
        print_success "Docker is running"
    else
        print_warning "Docker is not running"
    fi
    
    if command -v docker-compose &> /dev/null || docker compose version &> /dev/null; then
        print_success "Docker Compose is available"
    else
        print_warning "Docker Compose is not available"
    fi
}

# Check environment files
check_environment() {
    print_status "Checking environment files..."
    
    if [ -f "apps/backend/.env" ]; then
        print_success "Backend .env file exists"
        
        # Check for Upstox configuration
        if grep -q "UPSTOX_API_KEY" apps/backend/.env; then
            if grep -q "UPSTOX_API_KEY=$" apps/backend/.env; then
                print_warning "Upstox API key not configured"
            else
                print_success "Upstox API key configured"
            fi
        else
            print_warning "Upstox configuration not found"
        fi
    else
        print_warning "Backend .env file not found"
    fi
    
    if [ -f "apps/frontend/.env.local" ]; then
        print_success "Frontend .env.local file exists"
    else
        print_warning "Frontend .env.local file not found"
    fi
}

# Check Turbo configuration
check_turbo() {
    print_status "Checking Turbo configuration..."
    
    if [ -f "turbo.json" ]; then
        print_success "turbo.json exists"
    else
        print_error "turbo.json not found"
        return 1
    fi
    
    if command -v turbo &> /dev/null; then
        print_success "Turbo CLI is available"
    else
        print_warning "Turbo CLI not found globally"
    fi
}

# Check ports
check_ports() {
    print_status "Checking port availability..."
    
    PORTS=(3000 3001 5432 8086 6379)
    
    for port in "${PORTS[@]}"; do
        if lsof -i :$port &> /dev/null; then
            print_warning "Port $port is in use"
        else
            print_success "Port $port is available"
        fi
    done
}

# Main verification function
main() {
    echo "🔍 Trading Platform Setup Verification"
    echo "======================================"
    echo ""
    
    ERRORS=0
    
    check_nodejs || ((ERRORS++))
    check_npm || ((ERRORS++))
    check_package_manager || ((ERRORS++))
    check_workspaces || ((ERRORS++))
    check_dependencies || ((ERRORS++))
    check_docker
    check_environment
    check_turbo || ((ERRORS++))
    check_ports
    
    echo ""
    
    if [ $ERRORS -eq 0 ]; then
        echo "🎉 All critical checks passed!"
        echo ""
        echo "You can now run:"
        echo "  npm run dev    # Start development servers"
        echo "  npm run build  # Build all applications"
        echo "  npm run test   # Run tests"
    else
        echo "❌ $ERRORS critical issues found."
        echo ""
        echo "Please fix the issues above before proceeding."
        echo "Run 'npm run setup' to automatically fix common issues."
    fi
    
    echo ""
}

# Run verification
main "$@"
