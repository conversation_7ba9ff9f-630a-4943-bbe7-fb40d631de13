#!/usr/bin/env node

/**
 * Fix workspace dependencies for npm compatibility
 * Replaces workspace:* with proper version references
 */

const fs = require('fs');
const path = require('path');

function fixWorkspaceDependencies() {
  console.log('🔧 Fixing workspace dependencies for npm compatibility...');
  
  const packageFiles = [
    'apps/backend/package.json',
    'apps/frontend/package.json'
  ];
  
  let fixed = 0;
  
  for (const packageFile of packageFiles) {
    if (fs.existsSync(packageFile)) {
      try {
        const content = fs.readFileSync(packageFile, 'utf8');
        const packageJson = JSON.parse(content);
        
        let modified = false;
        
        // Fix dependencies
        if (packageJson.dependencies) {
          for (const [name, version] of Object.entries(packageJson.dependencies)) {
            if (version === 'workspace:*') {
              packageJson.dependencies[name] = '1.0.0';
              modified = true;
              console.log(`  ✅ Fixed ${name} in ${packageFile}`);
            }
          }
        }
        
        // Fix devDependencies
        if (packageJson.devDependencies) {
          for (const [name, version] of Object.entries(packageJson.devDependencies)) {
            if (version === 'workspace:*') {
              packageJson.devDependencies[name] = '1.0.0';
              modified = true;
              console.log(`  ✅ Fixed ${name} in ${packageFile}`);
            }
          }
        }
        
        if (modified) {
          fs.writeFileSync(packageFile, JSON.stringify(packageJson, null, 2) + '\n');
          fixed++;
        }
        
      } catch (error) {
        console.log(`  ⚠️  Could not process ${packageFile}: ${error.message}`);
      }
    } else {
      console.log(`  ⚠️  File not found: ${packageFile}`);
    }
  }
  
  console.log(`\n✅ Fixed ${fixed} package.json files`);
  
  if (fixed > 0) {
    console.log('\nNext steps:');
    console.log('1. Delete node_modules and package-lock.json');
    console.log('2. Run npm install');
    console.log('\nOr run: npm run clean-install');
  }
}

fixWorkspaceDependencies();
