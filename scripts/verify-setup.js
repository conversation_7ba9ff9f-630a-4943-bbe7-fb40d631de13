#!/usr/bin/env node

/**
 * Verification Script for Trading Platform Setup
 * Node.js version for cross-platform compatibility
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
};

function print(type, message) {
  const symbols = {
    status: `${colors.blue}[CHECK]${colors.reset}`,
    success: `${colors.green}[✓]${colors.reset}`,
    warning: `${colors.yellow}[⚠]${colors.reset}`,
    error: `${colors.red}[✗]${colors.reset}`,
  };
  console.log(`${symbols[type]} ${message}`);
}

function checkCommand(command) {
  try {
    execSync(`which ${command}`, { stdio: 'ignore' });
    return true;
  } catch {
    try {
      execSync(`where ${command}`, { stdio: 'ignore' });
      return true;
    } catch {
      return false;
    }
  }
}

function getVersion(command) {
  try {
    return execSync(`${command} --version`, { encoding: 'utf8' }).trim();
  } catch {
    return null;
  }
}

function checkNodejs() {
  print('status', 'Checking Node.js version...');
  
  if (!checkCommand('node')) {
    print('error', 'Node.js is not installed');
    return false;
  }
  
  const version = process.version;
  const major = parseInt(version.slice(1).split('.')[0]);
  
  if (major >= 18) {
    print('success', `Node.js version: ${version}`);
    return true;
  } else {
    print('error', `Node.js version ${version} is too old. Requires 18+`);
    return false;
  }
}

function checkNpm() {
  print('status', 'Checking npm version...');
  
  if (!checkCommand('npm')) {
    print('error', 'npm is not installed');
    return false;
  }
  
  const version = getVersion('npm');
  if (!version) {
    print('error', 'Could not determine npm version');
    return false;
  }
  
  const major = parseInt(version.split('.')[0]);
  
  if (major >= 9) {
    print('success', `npm version: ${version}`);
    return true;
  } else {
    print('warning', `npm version ${version} is old. Recommended: 9+`);
    return true; // Not critical
  }
}

function checkPackageManager() {
  print('status', 'Checking package manager configuration...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    if (packageJson.packageManager) {
      print('success', `Package manager configured: ${packageJson.packageManager}`);
      return true;
    } else {
      print('error', 'Package manager not specified in package.json');
      return false;
    }
  } catch (error) {
    print('error', 'Could not read package.json');
    return false;
  }
}

function checkWorkspaces() {
  print('status', 'Checking workspace configuration...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    if (packageJson.workspaces) {
      print('success', 'Workspaces configured');
    } else {
      print('error', 'Workspaces not configured');
      return false;
    }
    
    // Check if workspace directories exist
    const workspaces = ['apps/backend', 'apps/frontend', 'packages/shared'];
    let allExist = true;
    
    for (const workspace of workspaces) {
      if (fs.existsSync(workspace)) {
        print('success', `Workspace exists: ${workspace}`);
      } else {
        print('error', `Workspace missing: ${workspace}`);
        allExist = false;
      }
    }
    
    return allExist;
  } catch (error) {
    print('error', 'Could not check workspace configuration');
    return false;
  }
}

function checkDependencies() {
  print('status', 'Checking dependencies...');
  
  if (fs.existsSync('package-lock.json')) {
    print('success', 'package-lock.json exists');
  } else {
    print('warning', 'package-lock.json not found. Run "npm install"');
  }
  
  if (fs.existsSync('node_modules')) {
    print('success', 'node_modules directory exists');
    return true;
  } else {
    print('error', 'node_modules not found. Run "npm install"');
    return false;
  }
}

function checkDocker() {
  print('status', 'Checking Docker...');
  
  if (!checkCommand('docker')) {
    print('warning', 'Docker is not installed');
    return true; // Not critical for development
  }
  
  try {
    execSync('docker ps', { stdio: 'ignore' });
    print('success', 'Docker is running');
  } catch {
    print('warning', 'Docker is not running');
  }
  
  if (checkCommand('docker-compose') || checkCommand('docker')) {
    print('success', 'Docker Compose is available');
  } else {
    print('warning', 'Docker Compose is not available');
  }
  
  return true;
}

function checkEnvironment() {
  print('status', 'Checking environment files...');
  
  if (fs.existsSync('apps/backend/.env')) {
    print('success', 'Backend .env file exists');
    
    try {
      const envContent = fs.readFileSync('apps/backend/.env', 'utf8');
      
      if (envContent.includes('UPSTOX_API_KEY')) {
        if (envContent.includes('UPSTOX_API_KEY=') && !envContent.includes('UPSTOX_API_KEY=your-')) {
          print('success', 'Upstox API key configured');
        } else {
          print('warning', 'Upstox API key not configured');
        }
      } else {
        print('warning', 'Upstox configuration not found');
      }
    } catch {
      print('warning', 'Could not read .env file');
    }
  } else {
    print('warning', 'Backend .env file not found');
  }
  
  if (fs.existsSync('apps/frontend/.env.local')) {
    print('success', 'Frontend .env.local file exists');
  } else {
    print('warning', 'Frontend .env.local file not found');
  }
  
  return true;
}

function checkTurbo() {
  print('status', 'Checking Turbo configuration...');
  
  if (fs.existsSync('turbo.json')) {
    print('success', 'turbo.json exists');
  } else {
    print('error', 'turbo.json not found');
    return false;
  }
  
  if (checkCommand('turbo')) {
    print('success', 'Turbo CLI is available');
  } else {
    print('warning', 'Turbo CLI not found globally');
  }
  
  return true;
}

function main() {
  console.log('🔍 Trading Platform Setup Verification');
  console.log('======================================');
  console.log('');
  
  let errors = 0;
  
  if (!checkNodejs()) errors++;
  if (!checkNpm()) errors++;
  if (!checkPackageManager()) errors++;
  if (!checkWorkspaces()) errors++;
  if (!checkDependencies()) errors++;
  checkDocker(); // Not critical
  checkEnvironment(); // Not critical
  if (!checkTurbo()) errors++;
  
  console.log('');
  
  if (errors === 0) {
    console.log('🎉 All critical checks passed!');
    console.log('');
    console.log('You can now run:');
    console.log('  npm run dev    # Start development servers');
    console.log('  npm run build  # Build all applications');
    console.log('  npm run test   # Run tests');
  } else {
    console.log(`❌ ${errors} critical issues found.`);
    console.log('');
    console.log('Please fix the issues above before proceeding.');
    console.log('Run "npm run setup" to automatically fix common issues.');
  }
  
  console.log('');
  
  process.exit(errors > 0 ? 1 : 0);
}

// Run verification
main();
