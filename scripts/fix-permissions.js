#!/usr/bin/env node

/**
 * Fix script permissions for cross-platform compatibility
 */

const fs = require('fs');
const path = require('path');

function fixPermissions() {
  const scriptsDir = path.join(__dirname);
  const scripts = ['setup.sh', 'verify-setup.sh'];
  
  console.log('🔧 Fixing script permissions...');
  
  for (const script of scripts) {
    const scriptPath = path.join(scriptsDir, script);
    
    if (fs.existsSync(scriptPath)) {
      try {
        // Make script executable (755 permissions)
        fs.chmodSync(scriptPath, 0o755);
        console.log(`✅ Fixed permissions for ${script}`);
      } catch (error) {
        console.log(`⚠️  Could not fix permissions for ${script}: ${error.message}`);
        console.log(`   You can run: chmod +x scripts/${script}`);
      }
    } else {
      console.log(`⚠️  Script not found: ${script}`);
    }
  }
  
  console.log('');
  console.log('Script permissions have been updated.');
  console.log('You can now run:');
  console.log('  npm run verify     # Node.js version (recommended)');
  console.log('  npm run verify:bash # Bash version');
  console.log('  npm run setup      # Full setup script');
}

fixPermissions();
