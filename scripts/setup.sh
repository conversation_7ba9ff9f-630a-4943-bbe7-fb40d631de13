#!/bin/bash

# Trading Platform Setup Script
# This script automates the setup process for the algorithmic trading platform

set -e

echo "🚀 Setting up Algorithmic Trading Platform..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."

    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ and try again."
        exit 1
    fi

    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node -v)"
        exit 1
    fi

    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm and try again."
        exit 1
    fi

    # Check npm version
    NPM_VERSION=$(npm -v | cut -d'.' -f1)
    if [ "$NPM_VERSION" -lt 9 ]; then
        print_warning "npm version 9+ is recommended. Current version: $(npm -v)"
        print_warning "Consider updating with: npm install -g npm@latest"
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker and try again."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    
    print_success "All prerequisites are installed"
}

# Verify package manager configuration
verify_package_manager() {
    print_status "Verifying package manager configuration..."

    # Check if packageManager is set in package.json
    if ! grep -q '"packageManager"' package.json; then
        print_warning "Package manager not specified in package.json"
        print_status "Adding package manager specification..."

        # Get current npm version
        NPM_VERSION=$(npm -v)

        # Add packageManager field using a simple approach
        if command -v jq &> /dev/null; then
            # Use jq if available
            jq --arg version "npm@$NPM_VERSION" '.packageManager = $version' package.json > package.json.tmp && mv package.json.tmp package.json
        else
            # Fallback: manual insertion
            sed -i '/"private": true,/a\  "packageManager": "npm@'$NPM_VERSION'",' package.json
        fi

        print_success "Package manager specification added"
    else
        print_success "Package manager already configured"
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    npm install
    print_success "Dependencies installed"
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "apps/backend/.env" ]; then
        cp apps/backend/.env.example apps/backend/.env
        print_success "Created apps/backend/.env"
    else
        print_warning "apps/backend/.env already exists, skipping..."
    fi
    
    # Frontend environment
    if [ ! -f "apps/frontend/.env.local" ]; then
        cp apps/frontend/.env.example apps/frontend/.env.local
        print_success "Created apps/frontend/.env.local"
    else
        print_warning "apps/frontend/.env.local already exists, skipping..."
    fi
}

# Start databases
start_databases() {
    print_status "Starting database services..."
    
    # Use docker-compose or docker compose based on availability
    if command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
    else
        DOCKER_COMPOSE_CMD="docker compose"
    fi
    
    $DOCKER_COMPOSE_CMD up -d postgres influxdb redis
    
    print_status "Waiting for databases to be ready..."
    sleep 30
    
    # Check if databases are healthy
    print_status "Checking database health..."
    
    # Wait for PostgreSQL
    until $DOCKER_COMPOSE_CMD exec postgres pg_isready -U postgres > /dev/null 2>&1; do
        print_status "Waiting for PostgreSQL..."
        sleep 5
    done
    
    # Wait for Redis
    until $DOCKER_COMPOSE_CMD exec redis redis-cli ping > /dev/null 2>&1; do
        print_status "Waiting for Redis..."
        sleep 5
    done
    
    print_success "Database services are ready"
}

# Run database migrations
run_migrations() {
    print_status "Running database migrations..."
    npm run db:migrate
    print_success "Database migrations completed"
}

# Seed database
seed_database() {
    print_status "Seeding database with initial data..."
    npm run db:seed
    print_success "Database seeded with demo accounts"
}

# Check Upstox configuration
check_upstox_config() {
    print_status "Checking Upstox configuration..."
    
    if [ -f "apps/backend/.env" ]; then
        if grep -q "UPSTOX_API_KEY=" apps/backend/.env && grep -q "UPSTOX_API_SECRET=" apps/backend/.env; then
            if grep -q "UPSTOX_API_KEY=$" apps/backend/.env || grep -q "UPSTOX_API_SECRET=$" apps/backend/.env; then
                print_warning "Upstox API credentials are not configured"
                print_warning "Please update apps/backend/.env with your Upstox API credentials:"
                print_warning "  UPSTOX_API_KEY=your-api-key"
                print_warning "  UPSTOX_API_SECRET=your-api-secret"
                print_warning "  UPSTOX_ACCESS_TOKEN=your-access-token"
                print_warning "Visit https://developer.upstox.com/ to get your credentials"
            else
                print_success "Upstox API credentials are configured"
            fi
        else
            print_warning "Upstox configuration not found in .env file"
        fi
    fi
}

# Main setup function
main() {
    echo "🏗️  Algorithmic Trading Platform Setup"
    echo "======================================"
    echo ""
    
    check_prerequisites
    verify_package_manager
    install_dependencies
    setup_environment
    start_databases
    run_migrations
    seed_database
    check_upstox_config
    
    echo ""
    echo "🎉 Setup completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Configure Upstox API credentials in apps/backend/.env (if not done already)"
    echo "2. Start the development servers: npm run dev"
    echo "3. Access the application at http://localhost:3000"
    echo "4. Login with demo accounts:"
    echo "   - Admin: <EMAIL> / admin123"
    echo "   - Trader: <EMAIL> / trader123"
    echo "5. Sync instrument data (Admin only): POST /api/instruments/sync"
    echo "6. Access market data dashboard at http://localhost:3000/dashboard/market"
    echo ""
    echo "For detailed documentation, see:"
    echo "- README.md"
    echo "- docs/UPSTOX_INTEGRATION.md"
    echo ""
}

# Run main function
main "$@"
