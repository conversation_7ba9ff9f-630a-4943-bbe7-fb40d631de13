const fs = require('fs');

console.log('🔍 Trading Platform Setup Verification');
console.log('======================================');
console.log('');

let errors = 0;

// Check Node.js version
console.log('[CHECK] Checking Node.js version...');
const nodeVersion = process.version;
const nodeMajor = parseInt(nodeVersion.slice(1).split('.')[0]);

if (nodeMajor >= 18) {
  console.log(`[✓] Node.js version: ${nodeVersion}`);
} else {
  console.log(`[✗] Node.js version ${nodeVersion} is too old. Requires 18+`);
  errors++;
}

// Check package.json
console.log('[CHECK] Checking package.json...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  if (packageJson.packageManager) {
    console.log(`[✓] Package manager configured: ${packageJson.packageManager}`);
  } else {
    console.log('[✗] Package manager not specified in package.json');
    errors++;
  }
  
  if (packageJson.workspaces) {
    console.log('[✓] Workspaces configured');
  } else {
    console.log('[✗] Workspaces not configured');
    errors++;
  }
} catch (error) {
  console.log('[✗] Could not read package.json');
  errors++;
}

// Check workspace directories
console.log('[CHECK] Checking workspace directories...');
const workspaces = ['apps/backend', 'apps/frontend', 'packages/shared'];

for (const workspace of workspaces) {
  if (fs.existsSync(workspace)) {
    console.log(`[✓] Workspace exists: ${workspace}`);
  } else {
    console.log(`[✗] Workspace missing: ${workspace}`);
    errors++;
  }
}

// Check dependencies
console.log('[CHECK] Checking dependencies...');
if (fs.existsSync('node_modules')) {
  console.log('[✓] node_modules directory exists');
} else {
  console.log('[✗] node_modules not found. Run "npm install"');
  errors++;
}

if (fs.existsSync('package-lock.json')) {
  console.log('[✓] package-lock.json exists');
} else {
  console.log('[⚠] package-lock.json not found. Run "npm install"');
}

// Check turbo.json
console.log('[CHECK] Checking Turbo configuration...');
if (fs.existsSync('turbo.json')) {
  console.log('[✓] turbo.json exists');
} else {
  console.log('[✗] turbo.json not found');
  errors++;
}

// Check environment files
console.log('[CHECK] Checking environment files...');
if (fs.existsSync('apps/backend/.env')) {
  console.log('[✓] Backend .env file exists');
} else {
  console.log('[⚠] Backend .env file not found');
}

if (fs.existsSync('apps/frontend/.env.local')) {
  console.log('[✓] Frontend .env.local file exists');
} else {
  console.log('[⚠] Frontend .env.local file not found');
}

console.log('');

if (errors === 0) {
  console.log('🎉 All critical checks passed!');
  console.log('');
  console.log('You can now run:');
  console.log('  npm run dev    # Start development servers');
  console.log('  npm run build  # Build all applications');
  console.log('  npm run test   # Run tests');
} else {
  console.log(`❌ ${errors} critical issues found.`);
  console.log('');
  console.log('Please fix the issues above before proceeding.');
  console.log('Run "npm run setup" to automatically fix common issues.');
}

console.log('');
