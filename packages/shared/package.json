{"name": "@trading-platform/shared", "version": "1.0.0", "description": "Shared types and utilities", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}, "dependencies": {"zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "jest": "^29.0.0"}}