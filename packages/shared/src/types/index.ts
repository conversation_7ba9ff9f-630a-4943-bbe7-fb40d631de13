import { z } from 'zod';

// User and Authentication Types
export const UserRoleSchema = z.enum(['admin', 'trader', 'viewer']);
export type UserRole = z.infer<typeof UserRoleSchema>;

export const UserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  role: UserRoleSchema,
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type User = z.infer<typeof UserSchema>;

export const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});

export const RegisterSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
});

// Market Data Types
export const MarketDataSchema = z.object({
  symbol: z.string(),
  price: z.number(),
  volume: z.number(),
  timestamp: z.date(),
  bid: z.number().optional(),
  ask: z.number().optional(),
  high: z.number().optional(),
  low: z.number().optional(),
  open: z.number().optional(),
  close: z.number().optional(),
});

export type MarketData = z.infer<typeof MarketDataSchema>;

export const CandlestickSchema = z.object({
  symbol: z.string(),
  timestamp: z.date(),
  open: z.number(),
  high: z.number(),
  low: z.number(),
  close: z.number(),
  volume: z.number(),
});

export type Candlestick = z.infer<typeof CandlestickSchema>;

// Trading Strategy Types
export const StrategyStatusSchema = z.enum(['draft', 'active', 'paused', 'stopped']);
export type StrategyStatus = z.infer<typeof StrategyStatusSchema>;

export const StrategyTypeSchema = z.enum(['momentum', 'mean_reversion', 'arbitrage', 'custom']);
export type StrategyType = z.infer<typeof StrategyTypeSchema>;

export const TradingStrategySchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  description: z.string().optional(),
  type: StrategyTypeSchema,
  status: StrategyStatusSchema,
  userId: z.string().uuid(),
  parameters: z.record(z.any()),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type TradingStrategy = z.infer<typeof TradingStrategySchema>;

// Order Types
export const OrderTypeSchema = z.enum(['market', 'limit', 'stop', 'stop_limit']);
export const OrderSideSchema = z.enum(['buy', 'sell']);
export const OrderStatusSchema = z.enum(['pending', 'filled', 'cancelled', 'rejected']);

export const OrderSchema = z.object({
  id: z.string().uuid(),
  symbol: z.string(),
  type: OrderTypeSchema,
  side: OrderSideSchema,
  quantity: z.number().positive(),
  price: z.number().positive().optional(),
  stopPrice: z.number().positive().optional(),
  status: OrderStatusSchema,
  userId: z.string().uuid(),
  strategyId: z.string().uuid().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  filledAt: z.date().optional(),
});

export type Order = z.infer<typeof OrderSchema>;

// Portfolio Types
export const PositionSchema = z.object({
  id: z.string().uuid(),
  symbol: z.string(),
  quantity: z.number(),
  averagePrice: z.number(),
  currentPrice: z.number(),
  unrealizedPnL: z.number(),
  realizedPnL: z.number(),
  userId: z.string().uuid(),
  updatedAt: z.date(),
});

export type Position = z.infer<typeof PositionSchema>;

export const PortfolioSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  totalValue: z.number(),
  cashBalance: z.number(),
  totalPnL: z.number(),
  dayPnL: z.number(),
  positions: z.array(PositionSchema),
  updatedAt: z.date(),
});

export type Portfolio = z.infer<typeof PortfolioSchema>;

// WebSocket Message Types
export const WSMessageTypeSchema = z.enum([
  'market_data',
  'order_update',
  'portfolio_update',
  'strategy_update',
  'error',
  'ping',
  'pong'
]);

export const WSMessageSchema = z.object({
  type: WSMessageTypeSchema,
  data: z.any(),
  timestamp: z.date(),
});

export type WSMessage = z.infer<typeof WSMessageSchema>;

// API Response Types
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  timestamp: z.date(),
});

export type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
};

// Pagination Types
export const PaginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export type Pagination = z.infer<typeof PaginationSchema>;

export const PaginatedResponseSchema = z.object({
  data: z.array(z.any()),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    pages: z.number(),
  }),
});

export type PaginatedResponse<T = any> = {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
};

// Risk Management Types
export const RiskLimitSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  maxDailyLoss: z.number(),
  maxPositionSize: z.number(),
  maxLeverage: z.number(),
  allowedSymbols: z.array(z.string()),
  isActive: z.boolean(),
});

export type RiskLimit = z.infer<typeof RiskLimitSchema>;
