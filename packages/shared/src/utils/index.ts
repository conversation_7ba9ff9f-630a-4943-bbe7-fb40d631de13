// Utility functions shared across frontend and backend

export const formatCurrency = (amount: number, currency = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

export const formatPercentage = (value: number, decimals = 2): string => {
  return `${(value * 100).toFixed(decimals)}%`;
};

export const formatNumber = (value: number, decimals = 2): string => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
};

export const calculatePnL = (
  quantity: number,
  entryPrice: number,
  currentPrice: number,
  side: 'buy' | 'sell'
): number => {
  const multiplier = side === 'buy' ? 1 : -1;
  return quantity * (currentPrice - entryPrice) * multiplier;
};

export const calculatePercentageChange = (
  oldValue: number,
  newValue: number
): number => {
  if (oldValue === 0) return 0;
  return (newValue - oldValue) / oldValue;
};

export const generateId = (): string => {
  return crypto.randomUUID();
};

export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidPassword = (password: string): boolean => {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
};

export const sanitizeString = (str: string): string => {
  return str.replace(/[<>]/g, '');
};

export const roundToDecimals = (value: number, decimals: number): number => {
  return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals);
};

export const clamp = (value: number, min: number, max: number): number => {
  return Math.min(Math.max(value, min), max);
};

export const getTimeAgo = (date: Date): string => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;
  
  return date.toLocaleDateString();
};

export const parseSymbol = (symbol: string): { base: string; quote: string } => {
  // Handle common symbol formats like BTC/USD, BTCUSD, BTC-USD
  const separators = ['/', '-', '_'];
  
  for (const sep of separators) {
    if (symbol.includes(sep)) {
      const [base, quote] = symbol.split(sep);
      return { base: base.toUpperCase(), quote: quote.toUpperCase() };
    }
  }
  
  // Default parsing for symbols like BTCUSD
  if (symbol.length >= 6) {
    const base = symbol.slice(0, 3).toUpperCase();
    const quote = symbol.slice(3).toUpperCase();
    return { base, quote };
  }
  
  return { base: symbol.toUpperCase(), quote: 'USD' };
};

export const validateSymbol = (symbol: string): boolean => {
  const symbolRegex = /^[A-Z]{2,10}[\/\-_]?[A-Z]{2,10}$/;
  return symbolRegex.test(symbol.toUpperCase());
};

export const calculateRisk = (
  entryPrice: number,
  stopLoss: number,
  positionSize: number
): number => {
  return Math.abs(entryPrice - stopLoss) * positionSize;
};

export const calculatePositionSize = (
  accountBalance: number,
  riskPercentage: number,
  entryPrice: number,
  stopLoss: number
): number => {
  const riskAmount = accountBalance * (riskPercentage / 100);
  const riskPerShare = Math.abs(entryPrice - stopLoss);
  return riskPerShare > 0 ? riskAmount / riskPerShare : 0;
};

export const getMarketStatus = (timestamp: Date): 'open' | 'closed' | 'pre_market' | 'after_hours' => {
  const hour = timestamp.getHours();
  const day = timestamp.getDay();
  
  // Weekend
  if (day === 0 || day === 6) return 'closed';
  
  // Market hours (9:30 AM - 4:00 PM EST)
  if (hour >= 9.5 && hour < 16) return 'open';
  if (hour >= 4 && hour < 9.5) return 'pre_market';
  if (hour >= 16 && hour < 20) return 'after_hours';
  
  return 'closed';
};

export const constants = {
  MAX_POSITION_SIZE: 1000000,
  MIN_ORDER_SIZE: 0.01,
  MAX_LEVERAGE: 10,
  DEFAULT_SLIPPAGE: 0.001, // 0.1%
  WEBSOCKET_RECONNECT_DELAY: 5000,
  API_TIMEOUT: 30000,
  CACHE_TTL: 300000, // 5 minutes
} as const;
