# Permission Troubleshooting Guide

This guide helps resolve script permission issues commonly encountered when setting up the trading platform.

## Common Permission Errors

### Error: "Permission denied"
```bash
> ./scripts/verify-setup.sh
sh: 1: ./scripts/verify-setup.sh: Permission denied
```

## Solutions

### Solution 1: Use Node.js Scripts (Recommended)

The project includes Node.js versions of all scripts that don't require special permissions:

```bash
# Use Node.js verification (works on all platforms)
node scripts/simple-verify.js

# Or use the npm script
npm run verify
```

### Solution 2: Fix Script Permissions

#### On Linux/macOS:
```bash
# Make scripts executable
chmod +x scripts/setup.sh
chmod +x scripts/verify-setup.sh

# Or fix all scripts at once
chmod +x scripts/*.sh
```

#### On Windows (Git Bash/WSL):
```bash
# Same as Linux/macOS
chmod +x scripts/*.sh

# Or use the Node.js permission fixer
node scripts/fix-permissions.js
```

### Solution 3: Run Scripts with Bash Explicitly

```bash
# Run setup script
bash scripts/setup.sh

# Run verification script
bash scripts/verify-setup.sh

# Or use npm scripts
npm run setup
npm run verify:bash
```

### Solution 4: Manual Verification

If scripts don't work, you can manually verify your setup:

#### Check Node.js and npm versions:
```bash
node --version  # Should be 18.0.0+
npm --version   # Should be 9.0.0+
```

#### Check package.json configuration:
```bash
# Look for packageManager field
grep "packageManager" package.json

# Should show: "packageManager": "npm@x.x.x"
```

#### Check workspace structure:
```bash
# Verify directories exist
ls -la apps/
ls -la packages/

# Should show: backend, frontend, shared
```

#### Check dependencies:
```bash
# Verify node_modules exists
ls -la node_modules/

# If missing, run:
npm install
```

## Platform-Specific Issues

### Windows

#### PowerShell Execution Policy:
```powershell
# Check current policy
Get-ExecutionPolicy

# If restricted, set to RemoteSigned
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### Use Git Bash or WSL:
```bash
# Git Bash (recommended for Windows)
bash scripts/setup.sh

# Windows Subsystem for Linux
wsl bash scripts/setup.sh
```

### macOS

#### Gatekeeper Issues:
```bash
# If macOS blocks the script
sudo spctl --master-disable  # Temporarily disable Gatekeeper
# Run your script
sudo spctl --master-enable   # Re-enable Gatekeeper
```

#### Alternative:
```bash
# Right-click script in Finder > Open With > Terminal
# Or use System Preferences > Security & Privacy to allow
```

### Linux

#### SELinux Issues:
```bash
# Check SELinux status
sestatus

# If enforcing, temporarily set to permissive
sudo setenforce 0
# Run your script
sudo setenforce 1
```

## Alternative Setup Methods

### Method 1: Manual Setup

If scripts don't work, follow these manual steps:

1. **Install Dependencies:**
   ```bash
   npm install
   ```

2. **Create Environment Files:**
   ```bash
   cp apps/backend/.env.example apps/backend/.env
   cp apps/frontend/.env.example apps/frontend/.env.local
   ```

3. **Configure Upstox Credentials:**
   Edit `apps/backend/.env` and add:
   ```bash
   UPSTOX_API_KEY=your-api-key
   UPSTOX_API_SECRET=your-api-secret
   UPSTOX_ACCESS_TOKEN=your-access-token
   ```

4. **Start Docker Services:**
   ```bash
   docker-compose up -d postgres influxdb redis
   ```

5. **Run Database Migrations:**
   ```bash
   cd apps/backend && npm run db:migrate
   ```

6. **Seed Database:**
   ```bash
   cd apps/backend && npm run db:seed
   ```

### Method 2: Docker-Only Setup

Use Docker for everything:

```bash
# Build and start all services
docker-compose up -d --build

# Run migrations in container
docker-compose exec backend npm run db:migrate

# Seed database in container
docker-compose exec backend npm run db:seed
```

### Method 3: IDE Integration

Most IDEs can run npm scripts directly:

- **VS Code**: Use the npm scripts panel or terminal
- **WebStorm**: Use the npm tool window
- **Atom**: Use the script runner package

## Verification Checklist

Use this manual checklist if automated verification fails:

### ✅ Prerequisites
- [ ] Node.js 18.0.0+ installed (`node --version`)
- [ ] npm 9.0.0+ installed (`npm --version`)
- [ ] Docker installed and running (`docker ps`)
- [ ] Git installed (`git --version`)

### ✅ Project Structure
- [ ] `package.json` exists in root
- [ ] `apps/backend/` directory exists
- [ ] `apps/frontend/` directory exists
- [ ] `packages/shared/` directory exists
- [ ] `turbo.json` exists

### ✅ Configuration
- [ ] `packageManager` field in root `package.json`
- [ ] `workspaces` field in root `package.json`
- [ ] `apps/backend/.env` file exists
- [ ] Upstox credentials configured in `.env`

### ✅ Dependencies
- [ ] `node_modules/` directory exists
- [ ] `package-lock.json` exists
- [ ] All workspace packages have `node_modules/`

### ✅ Services
- [ ] PostgreSQL container running
- [ ] InfluxDB container running
- [ ] Redis container running
- [ ] Backend starts without errors
- [ ] Frontend starts without errors

## Getting Help

If you continue to have permission issues:

1. **Check your operating system and shell:**
   ```bash
   echo $SHELL
   uname -a
   ```

2. **Try different approaches:**
   - Use Node.js scripts instead of bash scripts
   - Run commands manually instead of using scripts
   - Use Docker for isolated environment

3. **Common fixes:**
   ```bash
   # Fix line endings (Windows)
   dos2unix scripts/*.sh
   
   # Fix ownership (Linux)
   sudo chown -R $USER:$USER .
   
   # Fix permissions recursively
   find scripts/ -name "*.sh" -exec chmod +x {} \;
   ```

4. **Create an issue:**
   If nothing works, create a GitHub issue with:
   - Your operating system and version
   - Node.js and npm versions
   - Complete error message
   - Steps you've tried

## Quick Commands Reference

```bash
# Essential commands that should always work:
node --version                    # Check Node.js
npm --version                     # Check npm
npm install                       # Install dependencies
npm run dev                       # Start development
npm run build                     # Build applications
npm run test                      # Run tests

# Docker commands:
docker-compose up -d              # Start services
docker-compose down               # Stop services
docker-compose logs -f            # View logs

# Manual verification:
node scripts/simple-verify.js     # Simple verification
ls -la apps/                      # Check structure
cat package.json | grep packageManager  # Check config
```

Remember: The Node.js scripts (`npm run verify`) should work on all platforms without permission issues!
