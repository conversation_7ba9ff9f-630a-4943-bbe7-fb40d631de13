# npm Workspace Troubleshooting

This guide helps resolve npm workspace-related issues, particularly the "Unsupported URL Type 'workspace:'" error.

## Common Error

```
npm error code EUNSUPPORTEDPROTOCOL
npm error Unsupported URL Type "workspace:": workspace:*
```

## Root Cause

This error occurs when:
1. Your npm version doesn't support the `workspace:*` protocol
2. The workspace dependencies are not properly configured
3. npm workspaces feature is not available in older npm versions

## Solutions

### Solution 1: Quick Fix (Recommended)

Run the automated fix script:

```bash
# Fix workspace dependencies automatically
npm run fix-workspace-deps

# Clean install
npm run clean-install
```

### Solution 2: Manual Fix

1. **Check npm version:**
   ```bash
   npm --version
   ```

2. **Update npm if needed:**
   ```bash
   # Update to latest npm
   npm install -g npm@latest
   
   # Or install a compatible version
   npm install -g npm@8.19.4
   ```

3. **Fix workspace dependencies manually:**
   
   Edit `apps/backend/package.json`:
   ```json
   {
     "dependencies": {
       "@trading-platform/shared": "1.0.0"  // Changed from "workspace:*"
     }
   }
   ```
   
   Edit `apps/frontend/package.json`:
   ```json
   {
     "dependencies": {
       "@trading-platform/shared": "1.0.0"  // Changed from "workspace:*"
     }
   }
   ```

4. **Clean and reinstall:**
   ```bash
   rm -rf node_modules package-lock.json
   rm -rf apps/*/node_modules packages/*/node_modules
   npm install
   ```

### Solution 3: Use npm 7+ with Workspaces

If you have npm 7+, you can use proper workspace configuration:

1. **Update package.json files to use file references:**
   
   ```json
   {
     "dependencies": {
       "@trading-platform/shared": "file:../../packages/shared"
     }
   }
   ```

2. **Or use relative paths:**
   ```json
   {
     "dependencies": {
       "@trading-platform/shared": "link:../../packages/shared"
     }
   }
   ```

### Solution 4: Disable Workspaces (Fallback)

If workspaces continue to cause issues:

1. **Remove workspace configuration from root package.json:**
   ```json
   {
     // Remove or comment out:
     // "workspaces": ["apps/*", "packages/*"]
   }
   ```

2. **Install dependencies in each package separately:**
   ```bash
   cd packages/shared && npm install
   cd ../../apps/backend && npm install
   cd ../frontend && npm install
   ```

## npm Version Compatibility

### npm 8.x (Recommended for this project)
- ✅ Supports basic workspaces
- ✅ Compatible with Node.js 18+
- ✅ Stable and widely used

### npm 7.x
- ✅ First version with workspace support
- ⚠️ Some workspace features may be limited

### npm 6.x and below
- ❌ No workspace support
- ❌ Will cause "workspace:" protocol errors
- 🔄 **Must upgrade to npm 7+**

## Verification Steps

After applying fixes:

1. **Check npm version:**
   ```bash
   npm --version  # Should be 7.0.0+
   ```

2. **Verify workspace configuration:**
   ```bash
   npm ls --workspaces
   ```

3. **Test installation:**
   ```bash
   npm install
   ```

4. **Verify dependencies:**
   ```bash
   npm ls @trading-platform/shared
   ```

## Alternative Package Managers

If npm continues to cause issues, consider these alternatives:

### Yarn (Recommended Alternative)

```bash
# Install Yarn
npm install -g yarn

# Update package.json
"packageManager": "yarn@3.6.0"

# Install dependencies
yarn install
```

### pnpm

```bash
# Install pnpm
npm install -g pnpm

# Update package.json
"packageManager": "pnpm@8.6.0"

# Install dependencies
pnpm install
```

## Automated Scripts

The project includes several scripts to help with workspace issues:

```bash
# Fix workspace dependencies
npm run fix-workspace-deps

# Clean install everything
npm run clean-install

# Full setup with fixes
npm run setup

# Verify setup
npm run verify
```

## Manual Workspace Setup

If automated scripts don't work, set up workspaces manually:

1. **Create shared package:**
   ```bash
   cd packages/shared
   npm init -y
   npm run build
   ```

2. **Link shared package to backend:**
   ```bash
   cd apps/backend
   npm link ../../packages/shared
   ```

3. **Link shared package to frontend:**
   ```bash
   cd apps/frontend
   npm link ../../packages/shared
   ```

## Troubleshooting Commands

```bash
# Check npm configuration
npm config list

# Check workspace configuration
npm ls --workspaces

# Clear npm cache
npm cache clean --force

# Verify package.json syntax
node -e "console.log(JSON.parse(require('fs').readFileSync('package.json')))"

# Check for conflicting dependencies
npm ls --depth=0

# Reinstall everything from scratch
rm -rf node_modules package-lock.json apps/*/node_modules packages/*/node_modules
npm install
```

## Common Issues and Fixes

### Issue: "Cannot resolve dependency"
```bash
# Fix: Clean install
npm run clean-install
```

### Issue: "Module not found @trading-platform/shared"
```bash
# Fix: Build shared package first
cd packages/shared && npm run build
cd ../.. && npm install
```

### Issue: "Workspace dependency not found"
```bash
# Fix: Use file references
# In package.json:
"@trading-platform/shared": "file:../../packages/shared"
```

### Issue: "npm ERR! peer dep missing"
```bash
# Fix: Install peer dependencies
npm install --legacy-peer-deps
```

## Environment-Specific Solutions

### Windows
```cmd
# Use PowerShell or Git Bash
# Ensure proper path separators
npm config set script-shell "C:\\Program Files\\git\\bin\\bash.exe"
```

### macOS/Linux
```bash
# Ensure proper permissions
sudo chown -R $(whoami) ~/.npm
```

### Docker
```dockerfile
# Use specific npm version in Dockerfile
RUN npm install -g npm@8.19.4
```

## Prevention

To avoid workspace issues in the future:

1. **Pin npm version** in package.json
2. **Use specific dependency versions** instead of workspace:*
3. **Test with clean installs** regularly
4. **Document npm version requirements** clearly
5. **Use CI/CD** to catch dependency issues early

## Getting Help

If workspace issues persist:

1. **Check npm logs:**
   ```bash
   cat ~/.npm/_logs/*debug*.log
   ```

2. **Create minimal reproduction:**
   ```bash
   mkdir test-workspace
   cd test-workspace
   npm init -y
   # Add minimal workspace config
   ```

3. **Report issue with:**
   - npm version (`npm --version`)
   - Node.js version (`node --version`)
   - Operating system
   - Complete error message
   - Steps to reproduce

## Quick Reference

```bash
# Essential commands for workspace issues:
npm --version                    # Check npm version
npm install -g npm@latest        # Update npm
npm run fix-workspace-deps       # Fix workspace dependencies
npm run clean-install           # Clean install
npm ls --workspaces             # List workspaces
npm cache clean --force         # Clear cache
```
