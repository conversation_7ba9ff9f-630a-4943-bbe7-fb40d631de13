# Upstox WebSocket API Integration

This document describes the comprehensive integration of Upstox WebSocket API for real-time market data in the algorithmic trading platform.

## Overview

The integration provides:
- Real-time market data streaming via Upstox WebSocket
- Instrument management and search functionality
- Time-series data storage in InfluxDB
- Frontend components for market data visualization
- Comprehensive error handling and monitoring

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │     Backend      │    │   Upstox API    │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │ WebSocket   │◄┼────┼►│ WebSocket    │◄┼────┼►│ WebSocket   │ │
│ │ Client      │ │    │ │ Relay        │ │    │ │ Feed        │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │ Market Data │◄┼────┼►│ Market Data  │◄┼────┼►│ REST API    │ │
│ │ Components  │ │    │ │ Service      │ │    │ │             │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   Data Storage   │
                       │                  │
                       │ ┌──────────────┐ │
                       │ │ PostgreSQL   │ │
                       │ │ (Instruments)│ │
                       │ └──────────────┘ │
                       │                  │
                       │ ┌──────────────┐ │
                       │ │ InfluxDB     │ │
                       │ │ (Time Series)│ │
                       │ └──────────────┘ │
                       │                  │
                       │ ┌──────────────┐ │
                       │ │ Redis        │ │
                       │ │ (Cache)      │ │
                       │ └──────────────┘ │
                       └──────────────────┘
```

## Setup Instructions

### 1. Upstox API Credentials

1. Register at [Upstox Developer Console](https://developer.upstox.com/)
2. Create a new app and get your API credentials
3. Add credentials to your `.env` file:

```bash
UPSTOX_API_KEY=your-api-key
UPSTOX_API_SECRET=your-api-secret
UPSTOX_REDIRECT_URI=http://localhost:3001/api/upstox/callback
UPSTOX_ACCESS_TOKEN=your-access-token
```

### 2. Database Setup

Run the new migration to create market data tables:

```bash
npm run db:migrate
```

### 3. Instrument Data Sync

Sync instrument data from Upstox (Admin only):

```bash
curl -X POST http://localhost:3001/api/instruments/sync \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## API Endpoints

### Instruments

- `GET /api/instruments/search?q=RELIANCE` - Search instruments
- `GET /api/instruments/popular` - Get popular instruments
- `GET /api/instruments/:instrumentKey` - Get instrument details
- `GET /api/instruments/stats` - Get instrument statistics
- `POST /api/instruments/sync` - Sync instruments from Upstox (Admin)

### Market Data

- `POST /api/market-data/current` - Get current market data
- `POST /api/market-data/subscribe` - Subscribe to real-time data
- `POST /api/market-data/unsubscribe` - Unsubscribe from data
- `GET /api/market-data/status` - Get service status
- `GET /api/market-data/historical` - Get historical data
- `POST /api/market-data/quotes` - Get market quotes

### WebSocket

- `WS /ws/market-data` - Real-time market data stream

## Frontend Components

### InstrumentSearch

Search and select instruments with autocomplete:

```tsx
import { InstrumentSearch } from '@/components/market/InstrumentSearch';

<InstrumentSearch
  onSelect={(instrument) => console.log(instrument)}
  placeholder="Search stocks..."
/>
```

### PriceDisplay

Display real-time price information:

```tsx
import { PriceDisplay } from '@/components/market/PriceDisplay';

<PriceDisplay
  instrument={instrument}
  tick={tick}
  size="lg"
  showVolume={true}
/>
```

### Watchlist

Manage and display watchlists:

```tsx
import { Watchlist } from '@/components/market/Watchlist';

<Watchlist
  title="My Watchlist"
  showHeader={true}
/>
```

## WebSocket Usage

### Frontend WebSocket Hook

```tsx
import { useWebSocket } from '@/hooks/useWebSocket';

const { status, subscribe, unsubscribe, marketData } = useWebSocket();

// Subscribe to instruments
subscribe(['NSE_EQ|INE002A01018', 'NSE_EQ|INE009A01021']);

// Access real-time data
const reliance = marketData.get('NSE_EQ|INE002A01018');
```

### WebSocket Message Format

```json
{
  "type": "tick",
  "data": {
    "instrument_key": "NSE_EQ|INE002A01018",
    "last_price": 2450.50,
    "net_change": 12.30,
    "volume": 1500000,
    "last_trade_time": 1640995200000
  },
  "timestamp": "2023-12-01T10:30:00.000Z"
}
```

## Data Storage

### PostgreSQL Tables

- `instruments` - Instrument metadata
- `market_data_cache` - Latest market data
- `market_data_subscriptions` - User subscriptions
- `watchlists` - User watchlists
- `upstox_tokens` - API tokens

### InfluxDB Schema

Time-series data stored in `market_tick` measurement:

```
market_tick,instrument_key=NSE_EQ|INE002A01018,exchange_token=2885 
  last_price=2450.50,
  volume=1500000i,
  net_change=12.30
  1640995200000000000
```

## Error Handling

### Backend Error Types

- `MarketDataError` - Market data service errors
- `ExternalServiceError` - Upstox API errors
- `ValidationError` - Input validation errors
- `AuthenticationError` - Authentication failures

### Frontend Error Handling

```tsx
const { error, clearError } = useMarketDataStore();

if (error) {
  return (
    <div className="error-message">
      {error}
      <button onClick={clearError}>Dismiss</button>
    </div>
  );
}
```

## Monitoring

### Health Checks

The system includes comprehensive health monitoring:

- Database connectivity (PostgreSQL, Redis, InfluxDB)
- Upstox API status
- WebSocket connection status
- Market data service health

### System Status Component

```tsx
import { SystemStatus } from '@/components/monitoring/SystemStatus';

<SystemStatus showDetails={true} />
```

## Rate Limiting

Upstox API has rate limits:
- 10 requests per second for REST API
- WebSocket connections are limited per user

The system implements:
- Request queuing and throttling
- Automatic retry with exponential backoff
- Error handling for rate limit exceeded

## Security Considerations

1. **API Keys**: Store securely in environment variables
2. **Access Tokens**: Implement token refresh mechanism
3. **WebSocket Auth**: Validate JWT tokens for WebSocket connections
4. **Rate Limiting**: Implement client-side rate limiting
5. **Data Validation**: Validate all incoming market data

## Performance Optimization

1. **Caching**: Redis cache for frequently accessed data
2. **Batching**: Batch database writes for tick data
3. **Connection Pooling**: Efficient database connections
4. **Memory Management**: Cleanup old market data
5. **Compression**: WebSocket message compression

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Check Upstox API credentials
   - Verify network connectivity
   - Check rate limits

2. **No Market Data**
   - Ensure instruments are subscribed
   - Check market hours
   - Verify WebSocket connection

3. **High Memory Usage**
   - Monitor market data cache size
   - Implement data cleanup policies
   - Check for memory leaks

### Debug Mode

Enable debug logging:

```bash
LOG_LEVEL=debug npm run dev
```

### Health Check

Check system health:

```bash
curl http://localhost:3001/api/health
```

## Testing

### Unit Tests

```bash
npm run test
```

### Integration Tests

```bash
npm run test:integration
```

### WebSocket Testing

Use the provided test utilities to simulate market data:

```typescript
import { MockWebSocketServer } from '@/test/utils/mockWebSocket';

const mockServer = new MockWebSocketServer();
mockServer.sendTick({
  instrument_key: 'NSE_EQ|INE002A01018',
  last_price: 2450.50,
  // ...
});
```

## Production Deployment

1. **Environment Variables**: Set all required environment variables
2. **Database Migration**: Run migrations on production database
3. **SSL/TLS**: Use secure WebSocket connections (wss://)
4. **Monitoring**: Set up application monitoring
5. **Backup**: Regular database backups
6. **Scaling**: Consider horizontal scaling for high load

## Support

For issues related to:
- Upstox API: Contact Upstox support
- Platform bugs: Create GitHub issue
- Feature requests: Submit feature request

## License

This integration is part of the trading platform and follows the same license terms.
