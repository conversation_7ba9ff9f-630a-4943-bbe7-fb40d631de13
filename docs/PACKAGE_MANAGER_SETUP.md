# Package Manager Setup

This document explains the package manager configuration for the algorithmic trading platform monorepo.

## Package Manager Configuration

The project is configured to use **npm** as the package manager with specific version requirements.

### Root Configuration

The root `package.json` specifies:

```json
{
  "packageManager": "npm@9.8.1",
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=9.0.0"
  }
}
```

### Node.js Version

The project requires Node.js 18.0.0 or higher. A `.nvmrc` file is provided for Node Version Manager users:

```bash
# Use the specified Node.js version
nvm use
```

## Troubleshooting Package Manager Issues

### Issue: "Package manager not specified"

If you encounter the error:
```
We did not find a package manager specified in your root package.json
```

**Solution 1: Verify package.json**
Ensure the root `package.json` contains:
```json
{
  "packageManager": "npm@9.8.1"
}
```

**Solution 2: Run Turbo codemod**
```bash
npx @turbo/codemod add-package-manager
```

**Solution 3: Manual fix**
Add the packageManager field manually to your root `package.json`:
```bash
# Check your npm version
npm --version

# Add to package.json (replace with your npm version)
"packageManager": "npm@9.8.1"
```

### Issue: Node.js version mismatch

**Check current version:**
```bash
node --version
npm --version
```

**Install correct Node.js version:**
```bash
# Using nvm (recommended)
nvm install 18.18.0
nvm use 18.18.0

# Or download from nodejs.org
```

### Issue: npm version too old

**Update npm:**
```bash
npm install -g npm@latest
```

## Monorepo Structure

The project uses npm workspaces for monorepo management:

```json
{
  "workspaces": [
    "apps/*",
    "packages/*"
  ]
}
```

### Workspace Commands

```bash
# Install dependencies for all workspaces
npm install

# Run command in specific workspace
npm run dev --workspace=@trading-platform/backend
npm run build --workspace=@trading-platform/frontend

# Run command in all workspaces
npm run build --workspaces

# Add dependency to specific workspace
npm install axios --workspace=@trading-platform/backend
```

## Turbo Configuration

The project uses Turborepo for build orchestration. Configuration is in `turbo.json`:

```json
{
  "$schema": "https://turbo.build/schema.json",
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": ["dist/**", ".next/**"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    }
  }
}
```

### Turbo Commands

```bash
# Run all builds
npm run build

# Run development servers
npm run dev

# Run tests
npm run test

# Clean all build artifacts
npm run clean
```

## Package Installation

### Root Dependencies

Install dependencies that are shared across all workspaces:

```bash
# Development tools (ESLint, Prettier, TypeScript)
npm install -D eslint prettier typescript

# Shared utilities
npm install lodash
```

### Workspace Dependencies

Install dependencies specific to a workspace:

```bash
# Backend dependencies
npm install fastify --workspace=@trading-platform/backend

# Frontend dependencies
npm install next react react-dom --workspace=@trading-platform/frontend

# Shared package dependencies
npm install zod --workspace=@trading-platform/shared
```

### Dependency Management Best Practices

1. **Shared dependencies**: Install in root for tools used across workspaces
2. **Workspace-specific**: Install in the specific workspace that needs them
3. **Version consistency**: Use exact versions for critical dependencies
4. **Security**: Regularly audit dependencies with `npm audit`

## Common Issues and Solutions

### Issue: "Cannot find module"

**Cause**: Missing dependency or incorrect workspace reference

**Solution**:
```bash
# Install missing dependency
npm install <package-name> --workspace=<workspace-name>

# Or install in root if shared
npm install <package-name>
```

### Issue: "Workspace not found"

**Cause**: Incorrect workspace name or configuration

**Solution**:
```bash
# List all workspaces
npm ls --workspaces

# Check workspace names in package.json files
```

### Issue: Build failures

**Cause**: Dependency version conflicts or missing builds

**Solution**:
```bash
# Clean and reinstall
npm run clean
rm -rf node_modules package-lock.json
npm install

# Build in correct order
npm run build
```

### Issue: Turbo cache issues

**Solution**:
```bash
# Clear Turbo cache
npx turbo clean

# Force rebuild
npm run build --force
```

## Development Workflow

### Initial Setup

```bash
# Clone repository
git clone <repository-url>
cd algorithmic-trading-platform

# Install dependencies
npm install

# Build shared packages
npm run build --workspace=@trading-platform/shared

# Start development
npm run dev
```

### Adding New Dependencies

```bash
# For backend
npm install <package> --workspace=@trading-platform/backend

# For frontend
npm install <package> --workspace=@trading-platform/frontend

# For shared utilities
npm install <package> --workspace=@trading-platform/shared

# For development tools (root)
npm install -D <package>
```

### Updating Dependencies

```bash
# Check outdated packages
npm outdated --workspaces

# Update specific package
npm update <package> --workspace=<workspace>

# Update all packages
npm update --workspaces
```

## CI/CD Considerations

### Package Lock

Always commit `package-lock.json` to ensure consistent installs:

```bash
# Install exact versions from lock file
npm ci
```

### Build Order

Ensure shared packages are built before dependent packages:

```json
{
  "pipeline": {
    "build": {
      "dependsOn": ["^build"]
    }
  }
}
```

### Caching

Use Turbo's caching for faster CI builds:

```bash
# Enable remote caching (optional)
npx turbo login
npx turbo link
```

## Troubleshooting Commands

```bash
# Check npm configuration
npm config list

# Verify workspace setup
npm ls --workspaces

# Check package manager
npm --version
node --version

# Clear npm cache
npm cache clean --force

# Reinstall everything
rm -rf node_modules package-lock.json
npm install

# Check for issues
npm doctor
```

## Alternative Package Managers

While the project is configured for npm, you can use other package managers:

### Using Yarn

```bash
# Install Yarn
npm install -g yarn

# Update package.json
"packageManager": "yarn@3.6.0"

# Install dependencies
yarn install
```

### Using pnpm

```bash
# Install pnpm
npm install -g pnpm

# Update package.json
"packageManager": "pnpm@8.6.0"

# Install dependencies
pnpm install
```

**Note**: If switching package managers, update all documentation and CI/CD scripts accordingly.

## Support

If you continue to have package manager issues:

1. Check Node.js and npm versions
2. Clear all caches and reinstall
3. Verify workspace configuration
4. Check for conflicting global packages
5. Consult the [npm documentation](https://docs.npmjs.com/)
6. Create an issue with your specific error message
