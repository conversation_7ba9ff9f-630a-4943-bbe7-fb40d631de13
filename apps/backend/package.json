{"name": "@trading-platform/backend", "version": "1.0.0", "description": "Trading platform backend API", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "db:migrate": "tsx src/scripts/migrate.ts", "db:seed": "tsx src/scripts/seed.ts", "clean": "rm -rf dist"}, "dependencies": {"@trading-platform/shared": "workspace:*", "fastify": "^4.24.0", "@fastify/cors": "^8.4.0", "@fastify/jwt": "^7.2.0", "@fastify/websocket": "^8.3.0", "@fastify/swagger": "^8.12.0", "@fastify/swagger-ui": "^2.0.0", "pg": "^8.11.0", "@influxdata/influxdb-client": "^1.33.0", "redis": "^4.6.0", "bcrypt": "^5.1.0", "jsonwebtoken": "^9.0.0", "zod": "^3.22.0", "winston": "^3.11.0", "dotenv": "^16.3.0", "node-cron": "^3.0.0", "ws": "^8.14.0", "axios": "^1.6.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/pg": "^8.10.0", "@types/bcrypt": "^5.0.0", "@types/jsonwebtoken": "^9.0.0", "@types/ws": "^8.5.0", "@types/node-cron": "^3.0.0", "typescript": "^5.0.0", "tsx": "^4.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "eslint": "^8.0.0"}}