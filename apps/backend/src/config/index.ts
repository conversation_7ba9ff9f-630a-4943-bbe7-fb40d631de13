import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export const config = {
  // Server
  port: parseInt(process.env.PORT || '3001', 10),
  nodeEnv: process.env.NODE_ENV || 'development',
  logLevel: process.env.LOG_LEVEL || 'info',

  // Database
  databaseUrl: process.env.DATABASE_URL!,
  influxdbUrl: process.env.INFLUXDB_URL!,
  influxdbToken: process.env.INFLUXDB_TOKEN!,
  influxdbOrg: process.env.INFLUXDB_ORG!,
  influxdbBucket: process.env.INFLUXDB_BUCKET!,
  redisUrl: process.env.REDIS_URL!,

  // Authentication
  jwtSecret: process.env.JWT_SECRET!,
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '7d',
  bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),

  // External APIs
  alphaVantageApiKey: process.env.ALPHA_VANTAGE_API_KEY,
  polygonApiKey: process.env.POLYGON_API_KEY,
  finnhubApiKey: process.env.FINNHUB_API_KEY,

  // Upstox API
  upstoxApiKey: process.env.UPSTOX_API_KEY,
  upstoxApiSecret: process.env.UPSTOX_API_SECRET,
  upstoxRedirectUri: process.env.UPSTOX_REDIRECT_URI,
  upstoxAccessToken: process.env.UPSTOX_ACCESS_TOKEN,
  upstoxWsUrl: process.env.UPSTOX_WS_URL || 'wss://ws-api.upstox.com/v3/portfolio/stream-feed',
  upstoxApiBaseUrl: process.env.UPSTOX_API_BASE_URL || 'https://api.upstox.com/v2',

  // WebSocket
  wsPort: parseInt(process.env.WS_PORT || '3002', 10),
  wsHeartbeatInterval: parseInt(process.env.WS_HEARTBEAT_INTERVAL || '30000', 10),

  // Rate Limiting
  rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100', 10),
  rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '60000', 10),

  // CORS
  corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
} as const;

// Validate required environment variables
const requiredEnvVars = [
  'DATABASE_URL',
  'INFLUXDB_URL',
  'INFLUXDB_TOKEN',
  'INFLUXDB_ORG',
  'INFLUXDB_BUCKET',
  'REDIS_URL',
  'JWT_SECRET',
];

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

export * from './database';
export * from './logger';
