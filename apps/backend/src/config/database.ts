import { Pool } from 'pg';
import { InfluxDB } from '@influxdata/influxdb-client';
import { createClient } from 'redis';
import { logger } from './logger';

// PostgreSQL Configuration
export const pgPool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// InfluxDB Configuration
export const influxDB = new InfluxDB({
  url: process.env.INFLUXDB_URL!,
  token: process.env.INFLUXDB_TOKEN!,
});

export const influxWriteApi = influxDB.getWriteApi(
  process.env.INFLUXDB_ORG!,
  process.env.INFLUXDB_BUCKET!,
  'ns'
);

export const influxQueryApi = influxDB.getQueryApi(process.env.INFLUXDB_ORG!);

// Redis Configuration
export const redisClient = createClient({
  url: process.env.REDIS_URL,
});

// Database connection handlers
export const connectDatabases = async (): Promise<void> => {
  try {
    // Test PostgreSQL connection
    const pgClient = await pgPool.connect();
    await pgClient.query('SELECT NOW()');
    pgClient.release();
    logger.info('PostgreSQL connected successfully');

    // Test Redis connection
    await redisClient.connect();
    logger.info('Redis connected successfully');

    // InfluxDB connection is tested on first write/query
    logger.info('InfluxDB client initialized');
  } catch (error) {
    logger.error('Database connection failed:', error);
    throw error;
  }
};

export const disconnectDatabases = async (): Promise<void> => {
  try {
    await pgPool.end();
    await redisClient.quit();
    influxDB.close();
    logger.info('All database connections closed');
  } catch (error) {
    logger.error('Error closing database connections:', error);
  }
};

// Database health check
export const checkDatabaseHealth = async (): Promise<{
  postgres: boolean;
  redis: boolean;
  influx: boolean;
}> => {
  const health = {
    postgres: false,
    redis: false,
    influx: false,
  };

  try {
    // Check PostgreSQL
    const pgClient = await pgPool.connect();
    await pgClient.query('SELECT 1');
    pgClient.release();
    health.postgres = true;
  } catch (error) {
    logger.error('PostgreSQL health check failed:', error);
  }

  try {
    // Check Redis
    await redisClient.ping();
    health.redis = true;
  } catch (error) {
    logger.error('Redis health check failed:', error);
  }

  try {
    // Check InfluxDB
    const result = await influxQueryApi.collectRows('buckets()');
    health.influx = Array.isArray(result);
  } catch (error) {
    logger.error('InfluxDB health check failed:', error);
  }

  return health;
};
