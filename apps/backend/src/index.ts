import Fastify from 'fastify';
import cors from '@fastify/cors';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import websocket from '@fastify/websocket';
import { config, connectDatabases, logger, createRequestLogger } from './config';
import authPlugin from './plugins/auth';
import authRoutes from './routes/auth';
import { globalErrorHandler } from './middleware/errorHandler';
import { HealthCheckService } from './services/HealthCheckService';
import { MarketDataService } from './services/MarketDataService';
import { UpstoxApiClient } from './services/UpstoxApiClient';
import { InstrumentRepository } from './database/repositories/InstrumentRepository';
import { pgPool } from './config/database';

// Create Fastify instance
const fastify = Fastify({
  logger: false, // We use our custom logger
});

// Register request logging
fastify.addHook('onRequest', createRequestLogger());

// Register CORS
fastify.register(cors, {
  origin: config.corsOrigin,
  credentials: true,
});

// Register Swagger documentation
fastify.register(swagger, {
  swagger: {
    info: {
      title: 'Trading Platform API',
      description: 'Comprehensive algorithmic trading platform API',
      version: '1.0.0',
    },
    host: `localhost:${config.port}`,
    schemes: ['http', 'https'],
    consumes: ['application/json'],
    produces: ['application/json'],
    securityDefinitions: {
      Bearer: {
        type: 'apiKey',
        name: 'Authorization',
        in: 'header',
        description: 'Enter: Bearer {token}',
      },
    },
    security: [{ Bearer: [] }],
  },
});

fastify.register(swaggerUi, {
  routePrefix: '/docs',
  uiConfig: {
    docExpansion: 'full',
    deepLinking: false,
  },
  staticCSP: true,
  transformStaticCSP: (header) => header,
});

// Register WebSocket support
fastify.register(websocket);

// Register authentication plugin
fastify.register(authPlugin);

// Register WebSocket plugin
fastify.register(require('./plugins/websocket').default);

// Register routes
fastify.register(authRoutes, { prefix: '/api/auth' });
fastify.register(require('./routes/instruments').default, { prefix: '/api/instruments' });
fastify.register(require('./routes/market-data').default, { prefix: '/api/market-data' });

// Health check endpoint
fastify.get('/api/health', async (request, reply) => {
  try {
    const healthCheckService = (fastify as any).healthCheckService;
    if (!healthCheckService) {
      return reply.code(503).send({
        status: 'error',
        timestamp: new Date().toISOString(),
        error: 'Health check service not available',
      });
    }

    const health = await healthCheckService.forceHealthCheck();
    const statusCode = health.status === 'healthy' ? 200 :
                      health.status === 'degraded' ? 200 : 503;

    return reply.code(statusCode).send(health);
  } catch (error) {
    logger.error('Health check failed:', error);
    return reply.code(503).send({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
    });
  }
});

// Global error handler
fastify.setErrorHandler(globalErrorHandler);

// 404 handler
fastify.setNotFoundHandler((request, reply) => {
  reply.code(404).send({
    success: false,
    error: 'Route not found',
    timestamp: new Date(),
  });
});

// Graceful shutdown
const gracefulShutdown = async (signal: string) => {
  logger.info(`Received ${signal}, shutting down gracefully...`);
  
  try {
    await fastify.close();
    const { disconnectDatabases } = await import('./config/database');
    await disconnectDatabases();
    logger.info('Server shut down successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start server
const start = async () => {
  try {
    // Connect to databases
    await connectDatabases();

    // Initialize services
    const instrumentRepo = new InstrumentRepository(pgPool);
    const marketDataService = new MarketDataService(instrumentRepo);
    const upstoxApi = new UpstoxApiClient();
    const healthCheckService = new HealthCheckService();

    // Initialize market data service
    await marketDataService.initialize();

    // Initialize health check service
    healthCheckService.initialize(marketDataService, upstoxApi);

    // Store services in fastify context
    fastify.decorate('marketDataService', marketDataService);
    fastify.decorate('upstoxApi', upstoxApi);
    fastify.decorate('healthCheckService', healthCheckService);

    // Start server
    await fastify.listen({
      port: config.port,
      host: '0.0.0.0',
    });

    logger.info(`Server started successfully on port ${config.port}`);
    logger.info(`API documentation available at http://localhost:${config.port}/docs`);
    logger.info('Market data service initialized and ready');
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

start();
