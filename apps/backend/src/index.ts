import Fastify from 'fastify';
import cors from '@fastify/cors';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import websocket from '@fastify/websocket';
import { config, connectDatabases, logger, createRequestLogger } from './config';
import authPlugin from './plugins/auth';
import authRoutes from './routes/auth';

// Create Fastify instance
const fastify = Fastify({
  logger: false, // We use our custom logger
});

// Register request logging
fastify.addHook('onRequest', createRequestLogger());

// Register CORS
fastify.register(cors, {
  origin: config.corsOrigin,
  credentials: true,
});

// Register Swagger documentation
fastify.register(swagger, {
  swagger: {
    info: {
      title: 'Trading Platform API',
      description: 'Comprehensive algorithmic trading platform API',
      version: '1.0.0',
    },
    host: `localhost:${config.port}`,
    schemes: ['http', 'https'],
    consumes: ['application/json'],
    produces: ['application/json'],
    securityDefinitions: {
      Bearer: {
        type: 'apiKey',
        name: 'Authorization',
        in: 'header',
        description: 'Enter: Bearer {token}',
      },
    },
    security: [{ Bearer: [] }],
  },
});

fastify.register(swaggerUi, {
  routePrefix: '/docs',
  uiConfig: {
    docExpansion: 'full',
    deepLinking: false,
  },
  staticCSP: true,
  transformStaticCSP: (header) => header,
});

// Register WebSocket support
fastify.register(websocket);

// Register authentication plugin
fastify.register(authPlugin);

// Register routes
fastify.register(authRoutes, { prefix: '/api/auth' });

// Health check endpoint
fastify.get('/api/health', async (request, reply) => {
  try {
    const { checkDatabaseHealth } = await import('./config/database');
    const dbHealth = await checkDatabaseHealth();
    
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      database: dbHealth,
      memory: process.memoryUsage(),
      version: process.version,
    };

    const allDbHealthy = Object.values(dbHealth).every(status => status);
    const statusCode = allDbHealthy ? 200 : 503;

    return reply.code(statusCode).send(health);
  } catch (error) {
    logger.error('Health check failed:', error);
    return reply.code(503).send({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
    });
  }
});

// Global error handler
fastify.setErrorHandler((error, request, reply) => {
  logger.error('Request error:', {
    error: error.message,
    stack: error.stack,
    url: request.url,
    method: request.method,
    headers: request.headers,
  });

  // Don't expose internal errors in production
  const isDevelopment = config.nodeEnv === 'development';
  const message = isDevelopment ? error.message : 'Internal server error';

  reply.code(error.statusCode || 500).send({
    success: false,
    error: message,
    timestamp: new Date(),
  });
});

// 404 handler
fastify.setNotFoundHandler((request, reply) => {
  reply.code(404).send({
    success: false,
    error: 'Route not found',
    timestamp: new Date(),
  });
});

// Graceful shutdown
const gracefulShutdown = async (signal: string) => {
  logger.info(`Received ${signal}, shutting down gracefully...`);
  
  try {
    await fastify.close();
    const { disconnectDatabases } = await import('./config/database');
    await disconnectDatabases();
    logger.info('Server shut down successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start server
const start = async () => {
  try {
    // Connect to databases
    await connectDatabases();
    
    // Start server
    await fastify.listen({
      port: config.port,
      host: '0.0.0.0',
    });

    logger.info(`Server started successfully on port ${config.port}`);
    logger.info(`API documentation available at http://localhost:${config.port}/docs`);
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

start();
