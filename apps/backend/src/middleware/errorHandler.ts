import { FastifyRequest, FastifyReply, FastifyError } from 'fastify';
import { logger } from '../config/logger';
import { ApiResponse } from '@trading-platform/shared';

export interface ErrorContext {
  userId?: string;
  requestId?: string;
  endpoint?: string;
  method?: string;
  userAgent?: string;
  ip?: string;
}

export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public context?: ErrorContext;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    context?: ErrorContext
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.context = context;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string, context?: ErrorContext) {
    super(message, 400, true, context);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required', context?: ErrorContext) {
    super(message, 401, true, context);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions', context?: ErrorContext) {
    super(message, 403, true, context);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found', context?: ErrorContext) {
    super(message, 404, true, context);
  }
}

export class ConflictError extends AppError {
  constructor(message: string, context?: ErrorContext) {
    super(message, 409, true, context);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded', context?: ErrorContext) {
    super(message, 429, true, context);
  }
}

export class ExternalServiceError extends AppError {
  constructor(service: string, message?: string, context?: ErrorContext) {
    super(message || `External service error: ${service}`, 502, true, context);
  }
}

export class MarketDataError extends AppError {
  constructor(message: string, context?: ErrorContext) {
    super(`Market data error: ${message}`, 503, true, context);
  }
}

/**
 * Extract error context from request
 */
function extractErrorContext(request: FastifyRequest): ErrorContext {
  return {
    userId: (request as any).user?.id,
    requestId: request.id,
    endpoint: request.url,
    method: request.method,
    userAgent: request.headers['user-agent'],
    ip: request.ip,
  };
}

/**
 * Log error with context
 */
function logError(error: Error, context: ErrorContext): void {
  const logData = {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString(),
  };

  if (error instanceof AppError) {
    logData.statusCode = error.statusCode;
    logData.isOperational = error.isOperational;
    
    if (error.statusCode >= 500) {
      logger.error('Application Error', logData);
    } else {
      logger.warn('Client Error', logData);
    }
  } else {
    logger.error('Unexpected Error', logData);
  }
}

/**
 * Send error response
 */
function sendErrorResponse(
  reply: FastifyReply,
  error: Error,
  context: ErrorContext
): void {
  let statusCode = 500;
  let message = 'Internal server error';
  let details: any = undefined;

  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
    
    // Include additional details for client errors in development
    if (process.env.NODE_ENV === 'development' && statusCode < 500) {
      details = {
        stack: error.stack,
        context: error.context,
      };
    }
  } else {
    // Don't expose internal error details in production
    if (process.env.NODE_ENV === 'development') {
      message = error.message;
      details = {
        stack: error.stack,
        context,
      };
    }
  }

  const response: ApiResponse = {
    success: false,
    error: message,
    timestamp: new Date(),
    ...(details && { details }),
  };

  reply.code(statusCode).send(response);
}

/**
 * Global error handler for Fastify
 */
export const globalErrorHandler = (
  error: FastifyError,
  request: FastifyRequest,
  reply: FastifyReply
): void => {
  const context = extractErrorContext(request);
  
  // Log the error
  logError(error, context);
  
  // Send error response
  sendErrorResponse(reply, error, context);
};

/**
 * Async error wrapper for route handlers
 */
export const asyncHandler = (
  fn: (request: FastifyRequest, reply: FastifyReply) => Promise<any>
) => {
  return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    try {
      await fn(request, reply);
    } catch (error) {
      const context = extractErrorContext(request);
      
      if (error instanceof AppError) {
        logError(error, context);
        sendErrorResponse(reply, error, context);
      } else {
        // Wrap unexpected errors
        const appError = new AppError(
          error instanceof Error ? error.message : 'Unknown error',
          500,
          false,
          context
        );
        logError(appError, context);
        sendErrorResponse(reply, appError, context);
      }
    }
  };
};

/**
 * Market data specific error handlers
 */
export const handleMarketDataError = (error: any, context?: ErrorContext): AppError => {
  if (error.code === 'ECONNREFUSED') {
    return new MarketDataError('Market data service unavailable', context);
  }
  
  if (error.code === 'ETIMEDOUT') {
    return new MarketDataError('Market data request timeout', context);
  }
  
  if (error.response?.status === 401) {
    return new AuthenticationError('Market data authentication failed', context);
  }
  
  if (error.response?.status === 429) {
    return new RateLimitError('Market data rate limit exceeded', context);
  }
  
  if (error.response?.status >= 500) {
    return new ExternalServiceError('Upstox', error.message, context);
  }
  
  return new MarketDataError(error.message || 'Unknown market data error', context);
};

/**
 * Database error handler
 */
export const handleDatabaseError = (error: any, context?: ErrorContext): AppError => {
  if (error.code === '23505') { // Unique violation
    return new ConflictError('Resource already exists', context);
  }
  
  if (error.code === '23503') { // Foreign key violation
    return new ValidationError('Referenced resource does not exist', context);
  }
  
  if (error.code === '23502') { // Not null violation
    return new ValidationError('Required field is missing', context);
  }
  
  if (error.code === 'ECONNREFUSED') {
    return new AppError('Database connection failed', 503, true, context);
  }
  
  return new AppError('Database error', 500, false, context);
};

/**
 * Validation error handler for Zod
 */
export const handleValidationError = (error: any, context?: ErrorContext): ValidationError => {
  if (error.name === 'ZodError') {
    const messages = error.errors.map((err: any) => 
      `${err.path.join('.')}: ${err.message}`
    ).join(', ');
    
    return new ValidationError(`Validation failed: ${messages}`, context);
  }
  
  return new ValidationError(error.message || 'Validation failed', context);
};

/**
 * Rate limiting error handler
 */
export const handleRateLimitError = (context?: ErrorContext): RateLimitError => {
  return new RateLimitError('Too many requests, please try again later', context);
};

/**
 * Create error response for WebSocket
 */
export const createWebSocketError = (
  type: string,
  message: string,
  code?: number
): any => {
  return {
    type: 'error',
    data: {
      error_type: type,
      message,
      code,
      timestamp: new Date().toISOString(),
    },
  };
};
