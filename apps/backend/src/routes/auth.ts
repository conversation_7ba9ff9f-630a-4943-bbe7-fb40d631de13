import { FastifyPluginAsync } from 'fastify';
import { LoginSchema, RegisterSchema, ApiResponse } from '@trading-platform/shared';

declare module 'fastify' {
  interface FastifyInstance {
    authService: any;
  }
}

const authRoutes: FastifyPluginAsync = async (fastify) => {
  // Register endpoint
  fastify.post<{
    Body: {
      email: string;
      password: string;
      firstName: string;
      lastName: string;
    };
  }>('/register', {
    schema: {
      body: {
        type: 'object',
        required: ['email', 'password', 'firstName', 'lastName'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 8 },
          firstName: { type: 'string', minLength: 1 },
          lastName: { type: 'string', minLength: 1 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    role: { type: 'string' },
                  },
                },
                token: { type: 'string' },
              },
            },
            timestamp: { type: 'string' },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { user, token } = await fastify.authService.register(request.body);
      
      const response: ApiResponse = {
        success: true,
        data: { user, token },
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Registration failed',
        timestamp: new Date(),
      };

      return reply.code(400).send(response);
    }
  });

  // Login endpoint
  fastify.post<{
    Body: {
      email: string;
      password: string;
    };
  }>('/login', {
    schema: {
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 1 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    role: { type: 'string' },
                  },
                },
                token: { type: 'string' },
              },
            },
            timestamp: { type: 'string' },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { user, token } = await fastify.authService.login(request.body);
      
      const response: ApiResponse = {
        success: true,
        data: { user, token },
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed',
        timestamp: new Date(),
      };

      return reply.code(401).send(response);
    }
  });

  // Refresh token endpoint
  fastify.post<{
    Body: {
      token: string;
    };
  }>('/refresh', {
    schema: {
      body: {
        type: 'object',
        required: ['token'],
        properties: {
          token: { type: 'string' },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { user, token } = await fastify.authService.refreshToken(request.body.token);
      
      const response: ApiResponse = {
        success: true,
        data: { user, token },
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Token refresh failed',
        timestamp: new Date(),
      };

      return reply.code(401).send(response);
    }
  });

  // Get current user endpoint
  fastify.get('/me', {
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                firstName: { type: 'string' },
                lastName: { type: 'string' },
                role: { type: 'string' },
              },
            },
            timestamp: { type: 'string' },
          },
        },
      },
    },
  }, async (request, reply) => {
    const response: ApiResponse = {
      success: true,
      data: request.user,
      timestamp: new Date(),
    };

    return reply.code(200).send(response);
  });

  // Change password endpoint
  fastify.post<{
    Body: {
      currentPassword: string;
      newPassword: string;
    };
  }>('/change-password', {
    schema: {
      body: {
        type: 'object',
        required: ['currentPassword', 'newPassword'],
        properties: {
          currentPassword: { type: 'string' },
          newPassword: { type: 'string', minLength: 8 },
        },
      },
    },
  }, async (request, reply) => {
    try {
      await fastify.authService.changePassword(
        request.user!.id,
        request.body.currentPassword,
        request.body.newPassword
      );
      
      const response: ApiResponse = {
        success: true,
        data: { message: 'Password changed successfully' },
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Password change failed',
        timestamp: new Date(),
      };

      return reply.code(400).send(response);
    }
  });

  // Reset password request endpoint
  fastify.post<{
    Body: {
      email: string;
    };
  }>('/reset-password', {
    schema: {
      body: {
        type: 'object',
        required: ['email'],
        properties: {
          email: { type: 'string', format: 'email' },
        },
      },
    },
  }, async (request, reply) => {
    try {
      await fastify.authService.resetPassword(request.body.email);
      
      const response: ApiResponse = {
        success: true,
        data: { message: 'If the email exists, a reset link has been sent' },
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Password reset request failed',
        timestamp: new Date(),
      };

      return reply.code(400).send(response);
    }
  });

  // Confirm password reset endpoint
  fastify.post<{
    Body: {
      token: string;
      newPassword: string;
    };
  }>('/confirm-reset', {
    schema: {
      body: {
        type: 'object',
        required: ['token', 'newPassword'],
        properties: {
          token: { type: 'string' },
          newPassword: { type: 'string', minLength: 8 },
        },
      },
    },
  }, async (request, reply) => {
    try {
      await fastify.authService.confirmPasswordReset(
        request.body.token,
        request.body.newPassword
      );
      
      const response: ApiResponse = {
        success: true,
        data: { message: 'Password reset successfully' },
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Password reset failed',
        timestamp: new Date(),
      };

      return reply.code(400).send(response);
    }
  });

  // Logout endpoint (client-side token removal)
  fastify.post('/logout', {
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' },
              },
            },
            timestamp: { type: 'string' },
          },
        },
      },
    },
  }, async (request, reply) => {
    const response: ApiResponse = {
      success: true,
      data: { message: 'Logged out successfully' },
      timestamp: new Date(),
    };

    return reply.code(200).send(response);
  });
};

export default authRoutes;
