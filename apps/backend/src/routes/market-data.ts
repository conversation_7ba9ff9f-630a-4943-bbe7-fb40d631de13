import { FastifyPluginAsync } from 'fastify';
import { ApiResponse, MarketDataSubscription } from '@trading-platform/shared';
import { MarketDataService } from '../services/MarketDataService';
import { InstrumentRepository } from '../database/repositories/InstrumentRepository';
import { pgPool } from '../config/database';
import { logger } from '../config/logger';

declare module 'fastify' {
  interface FastifyInstance {
    marketDataService: MarketDataService;
  }
}

const marketDataRoutes: FastifyPluginAsync = async (fastify) => {
  // Initialize services
  const instrumentRepo = new InstrumentRepository(pgPool);
  const marketDataService = new MarketDataService(instrumentRepo);
  
  fastify.decorate('marketDataService', marketDataService);

  // Initialize market data service
  marketDataService.initialize().catch(error => {
    logger.error('Failed to initialize market data service:', error);
  });

  // Get current market data for instruments
  fastify.post<{
    Body: {
      instrument_keys: string[];
    };
  }>('/current', {
    schema: {
      body: {
        type: 'object',
        required: ['instrument_keys'],
        properties: {
          instrument_keys: {
            type: 'array',
            items: { type: 'string' },
            minItems: 1,
            maxItems: 100,
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            timestamp: { type: 'string' },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { instrument_keys } = request.body;
      
      const marketData = await marketDataService.getCurrentData(instrument_keys);

      const response: ApiResponse = {
        success: true,
        data: marketData,
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      logger.error('Failed to get current market data:', error);
      
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get current market data',
        timestamp: new Date(),
      };

      return reply.code(500).send(response);
    }
  });

  // Subscribe to market data
  fastify.post<{
    Body: {
      subscriptions: MarketDataSubscription[];
    };
  }>('/subscribe', {
    schema: {
      body: {
        type: 'object',
        required: ['subscriptions'],
        properties: {
          subscriptions: {
            type: 'array',
            items: {
              type: 'object',
              required: ['instrument_key', 'mode'],
              properties: {
                instrument_key: { type: 'string' },
                mode: { type: 'string', enum: ['ltpc', 'full'] },
              },
            },
            minItems: 1,
            maxItems: 50,
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { subscriptions } = request.body;
      
      await marketDataService.subscribe(subscriptions);

      const response: ApiResponse = {
        success: true,
        data: { 
          message: `Subscribed to ${subscriptions.length} instruments`,
          subscriptions,
        },
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      logger.error('Failed to subscribe to market data:', error);
      
      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to subscribe to market data',
        timestamp: new Date(),
      };

      return reply.code(500).send(response);
    }
  });

  // Unsubscribe from market data
  fastify.post<{
    Body: {
      instrument_keys: string[];
    };
  }>('/unsubscribe', {
    schema: {
      body: {
        type: 'object',
        required: ['instrument_keys'],
        properties: {
          instrument_keys: {
            type: 'array',
            items: { type: 'string' },
            minItems: 1,
            maxItems: 50,
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { instrument_keys } = request.body;
      
      await marketDataService.unsubscribe(instrument_keys);

      const response: ApiResponse = {
        success: true,
        data: { 
          message: `Unsubscribed from ${instrument_keys.length} instruments`,
          instrument_keys,
        },
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      logger.error('Failed to unsubscribe from market data:', error);
      
      const response: ApiResponse = {
        success: false,
        error: 'Failed to unsubscribe from market data',
        timestamp: new Date(),
      };

      return reply.code(500).send(response);
    }
  });

  // Get market data service status
  fastify.get('/status', {
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                initialized: { type: 'boolean' },
                connected: { type: 'boolean' },
                subscribedCount: { type: 'number' },
                cacheSize: { type: 'number' },
                wsStatus: { type: 'object' },
              },
            },
            timestamp: { type: 'string' },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const status = marketDataService.getStatus();

      const response: ApiResponse = {
        success: true,
        data: status,
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      logger.error('Failed to get market data status:', error);
      
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get market data status',
        timestamp: new Date(),
      };

      return reply.code(500).send(response);
    }
  });

  // Get historical data from InfluxDB
  fastify.get<{
    Querystring: {
      instrument_key: string;
      start_time: string;
      end_time?: string;
      interval?: string;
    };
  }>('/historical', {
    schema: {
      querystring: {
        type: 'object',
        required: ['instrument_key', 'start_time'],
        properties: {
          instrument_key: { type: 'string' },
          start_time: { type: 'string' },
          end_time: { type: 'string' },
          interval: { type: 'string', default: '1m' },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { instrument_key, start_time, end_time, interval = '1m' } = request.query;
      
      // Query InfluxDB for historical data
      const { influxQueryApi } = await import('../config/database');
      
      const endTimeClause = end_time ? `and r._time <= time(v: "${end_time}")` : '';
      
      const query = `
        from(bucket: "${process.env.INFLUXDB_BUCKET}")
          |> range(start: time(v: "${start_time}") ${endTimeClause})
          |> filter(fn: (r) => r._measurement == "market_tick")
          |> filter(fn: (r) => r.instrument_key == "${instrument_key}")
          |> aggregateWindow(every: ${interval}, fn: mean, createEmpty: false)
          |> yield(name: "mean")
      `;

      const data = await influxQueryApi.collectRows(query);

      const response: ApiResponse = {
        success: true,
        data: data,
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      logger.error('Failed to get historical data:', error);
      
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get historical data',
        timestamp: new Date(),
      };

      return reply.code(500).send(response);
    }
  });

  // Get market quotes from Upstox API
  fastify.post<{
    Body: {
      instrument_keys: string[];
    };
  }>('/quotes', {
    schema: {
      body: {
        type: 'object',
        required: ['instrument_keys'],
        properties: {
          instrument_keys: {
            type: 'array',
            items: { type: 'string' },
            minItems: 1,
            maxItems: 20,
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { instrument_keys } = request.body;
      
      // Get quotes from Upstox API
      const { upstoxApi } = fastify;
      const quotes = await upstoxApi.getQuotes(instrument_keys);

      const response: ApiResponse = {
        success: true,
        data: quotes,
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      logger.error('Failed to get market quotes:', error);
      
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get market quotes',
        timestamp: new Date(),
      };

      return reply.code(500).send(response);
    }
  });

  // Cleanup on server shutdown
  fastify.addHook('onClose', async () => {
    await marketDataService.cleanup();
  });
};

export default marketDataRoutes;
