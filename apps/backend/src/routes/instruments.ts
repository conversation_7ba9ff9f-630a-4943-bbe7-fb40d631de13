import { FastifyPluginAsync } from 'fastify';
import { ApiResponse, Instrument } from '@trading-platform/shared';
import { InstrumentRepository } from '../database/repositories/InstrumentRepository';
import { UpstoxApiClient } from '../services/UpstoxApiClient';
import { pgPool } from '../config/database';
import { logger } from '../config/logger';

declare module 'fastify' {
  interface FastifyInstance {
    instrumentRepo: InstrumentRepository;
    upstoxApi: UpstoxApiClient;
  }
}

const instrumentRoutes: FastifyPluginAsync = async (fastify) => {
  // Initialize repositories and services
  const instrumentRepo = new InstrumentRepository(pgPool);
  const upstoxApi = new UpstoxApiClient();
  
  fastify.decorate('instrumentRepo', instrumentRepo);
  fastify.decorate('upstoxApi', upstoxApi);

  // Search instruments
  fastify.get<{
    Querystring: {
      q: string;
      limit?: number;
      exchanges?: string;
      segments?: string;
    };
  }>('/search', {
    schema: {
      querystring: {
        type: 'object',
        required: ['q'],
        properties: {
          q: { type: 'string', minLength: 1 },
          limit: { type: 'number', minimum: 1, maximum: 100, default: 20 },
          exchanges: { type: 'string' },
          segments: { type: 'string' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  instrument_key: { type: 'string' },
                  symbol: { type: 'string' },
                  name: { type: 'string' },
                  exchange: { type: 'string' },
                  segment: { type: 'string' },
                },
              },
            },
            timestamp: { type: 'string' },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { q, limit = 20, exchanges, segments } = request.query;
      
      const exchangeList = exchanges ? exchanges.split(',') : undefined;
      const segmentList = segments ? segments.split(',') : undefined;
      
      const instruments = await instrumentRepo.searchInstruments(
        q,
        limit,
        exchangeList,
        segmentList
      );

      const response: ApiResponse<Instrument[]> = {
        success: true,
        data: instruments,
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      logger.error('Failed to search instruments:', error);
      
      const response: ApiResponse = {
        success: false,
        error: 'Failed to search instruments',
        timestamp: new Date(),
      };

      return reply.code(500).send(response);
    }
  });

  // Get popular instruments
  fastify.get('/popular', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'number', minimum: 1, maximum: 100, default: 50 },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { limit = 50 } = request.query as { limit?: number };
      
      const instruments = await instrumentRepo.getPopularInstruments(limit);

      const response: ApiResponse<Instrument[]> = {
        success: true,
        data: instruments,
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      logger.error('Failed to get popular instruments:', error);
      
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get popular instruments',
        timestamp: new Date(),
      };

      return reply.code(500).send(response);
    }
  });

  // Get instrument by key
  fastify.get<{
    Params: {
      instrumentKey: string;
    };
  }>('/:instrumentKey', {
    schema: {
      params: {
        type: 'object',
        required: ['instrumentKey'],
        properties: {
          instrumentKey: { type: 'string' },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { instrumentKey } = request.params;
      
      const instrument = await instrumentRepo.getByInstrumentKey(instrumentKey);
      
      if (!instrument) {
        const response: ApiResponse = {
          success: false,
          error: 'Instrument not found',
          timestamp: new Date(),
        };
        
        return reply.code(404).send(response);
      }

      const response: ApiResponse<Instrument> = {
        success: true,
        data: instrument,
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      logger.error('Failed to get instrument:', error);
      
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get instrument',
        timestamp: new Date(),
      };

      return reply.code(500).send(response);
    }
  });

  // Get instruments by exchange
  fastify.get<{
    Querystring: {
      exchange: string;
      limit?: number;
    };
  }>('/exchange/:exchange', {
    schema: {
      params: {
        type: 'object',
        required: ['exchange'],
        properties: {
          exchange: { type: 'string' },
        },
      },
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'number', minimum: 1, maximum: 500, default: 100 },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { exchange } = request.params as { exchange: string };
      const { limit = 100 } = request.query as { limit?: number };
      
      const instruments = await instrumentRepo.getByExchange(exchange, limit);

      const response: ApiResponse<Instrument[]> = {
        success: true,
        data: instruments,
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      logger.error('Failed to get instruments by exchange:', error);
      
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get instruments by exchange',
        timestamp: new Date(),
      };

      return reply.code(500).send(response);
    }
  });

  // Get instrument statistics
  fastify.get('/stats', {
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                active: { type: 'number' },
                byExchange: { type: 'object' },
                bySegment: { type: 'object' },
              },
            },
            timestamp: { type: 'string' },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const stats = await instrumentRepo.getStats();

      const response: ApiResponse = {
        success: true,
        data: stats,
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      logger.error('Failed to get instrument stats:', error);
      
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get instrument statistics',
        timestamp: new Date(),
      };

      return reply.code(500).send(response);
    }
  });

  // Sync instruments from Upstox (Admin only)
  fastify.post('/sync', {
    preHandler: [fastify.requireRole(['admin'])],
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                synced: { type: 'number' },
                message: { type: 'string' },
              },
            },
            timestamp: { type: 'string' },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      logger.info('Starting instrument sync from Upstox...');
      
      // Fetch instruments from Upstox
      const upstoxInstruments = await upstoxApi.getInstruments();
      
      // Bulk upsert to database
      const syncedCount = await instrumentRepo.bulkUpsertInstruments(upstoxInstruments);

      const response: ApiResponse = {
        success: true,
        data: {
          synced: syncedCount,
          message: `Successfully synced ${syncedCount} instruments from Upstox`,
        },
        timestamp: new Date(),
      };

      return reply.code(200).send(response);
    } catch (error) {
      logger.error('Failed to sync instruments:', error);
      
      const response: ApiResponse = {
        success: false,
        error: 'Failed to sync instruments from Upstox',
        timestamp: new Date(),
      };

      return reply.code(500).send(response);
    }
  });
};

export default instrumentRoutes;
