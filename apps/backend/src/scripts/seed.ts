import bcrypt from 'bcrypt';
import { pgPool, logger, config } from '../config';

async function seedDatabase() {
  const client = await pgPool.connect();
  
  try {
    logger.info('Starting database seeding...');
    
    // Check if data already exists
    const userCount = await client.query('SELECT COUNT(*) FROM users');
    if (parseInt(userCount.rows[0].count) > 0) {
      logger.info('Database already seeded, skipping...');
      return;
    }
    
    await client.query('BEGIN');
    
    // Create admin user
    const adminPasswordHash = await bcrypt.hash('admin123', config.bcryptRounds);
    const adminResult = await client.query(`
      INSERT INTO users (email, password_hash, first_name, last_name, role)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id
    `, ['<EMAIL>', adminPasswordHash, 'Admin', 'User', 'admin']);
    
    const adminId = adminResult.rows[0].id;
    logger.info(`Created admin user with ID: ${adminId}`);
    
    // Create demo trader user
    const traderPasswordHash = await bcrypt.hash('trader123', config.bcryptRounds);
    const traderResult = await client.query(`
      INSERT INTO users (email, password_hash, first_name, last_name, role)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id
    `, ['<EMAIL>', traderPasswordHash, 'Demo', 'Trader', 'trader']);
    
    const traderId = traderResult.rows[0].id;
    logger.info(`Created trader user with ID: ${traderId}`);
    
    // Create portfolios for users
    await client.query(`
      INSERT INTO portfolios (user_id, cash_balance, total_value)
      VALUES ($1, $2, $3)
    `, [adminId, 100000, 100000]);
    
    await client.query(`
      INSERT INTO portfolios (user_id, cash_balance, total_value)
      VALUES ($1, $2, $3)
    `, [traderId, 50000, 50000]);
    
    logger.info('Created portfolios for users');
    
    // Create risk limits for users
    await client.query(`
      INSERT INTO risk_limits (user_id, max_daily_loss, max_position_size, max_leverage, allowed_symbols)
      VALUES ($1, $2, $3, $4, $5)
    `, [adminId, 10000, 50000, 5.0, ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN', 'NVDA', 'META']]);
    
    await client.query(`
      INSERT INTO risk_limits (user_id, max_daily_loss, max_position_size, max_leverage, allowed_symbols)
      VALUES ($1, $2, $3, $4, $5)
    `, [traderId, 5000, 25000, 3.0, ['AAPL', 'GOOGL', 'MSFT', 'TSLA']]);
    
    logger.info('Created risk limits for users');
    
    // Create sample trading strategies
    const strategies = [
      {
        name: 'Moving Average Crossover',
        description: 'Simple moving average crossover strategy',
        type: 'momentum',
        parameters: {
          shortPeriod: 10,
          longPeriod: 20,
          symbol: 'AAPL'
        }
      },
      {
        name: 'Mean Reversion',
        description: 'Mean reversion strategy using RSI',
        type: 'mean_reversion',
        parameters: {
          rsiPeriod: 14,
          oversoldLevel: 30,
          overboughtLevel: 70,
          symbol: 'GOOGL'
        }
      }
    ];
    
    for (const strategy of strategies) {
      await client.query(`
        INSERT INTO trading_strategies (user_id, name, description, type, parameters)
        VALUES ($1, $2, $3, $4, $5)
      `, [traderId, strategy.name, strategy.description, strategy.type, JSON.stringify(strategy.parameters)]);
    }
    
    logger.info('Created sample trading strategies');
    
    // Create sample notifications
    const notifications = [
      {
        type: 'welcome',
        title: 'Welcome to Trading Platform',
        message: 'Your account has been created successfully. Start by exploring the dashboard and creating your first trading strategy.'
      },
      {
        type: 'system',
        title: 'System Maintenance',
        message: 'Scheduled maintenance will occur this weekend from 2 AM to 4 AM EST.'
      }
    ];
    
    for (const notification of notifications) {
      await client.query(`
        INSERT INTO notifications (user_id, type, title, message)
        VALUES ($1, $2, $3, $4)
      `, [traderId, notification.type, notification.title, notification.message]);
    }
    
    logger.info('Created sample notifications');
    
    await client.query('COMMIT');
    logger.info('Database seeding completed successfully');
    
    // Log the created accounts
    logger.info('Created accounts:');
    logger.info('Admin: <EMAIL> / admin123');
    logger.info('Trader: <EMAIL> / trader123');
    
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Database seeding failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run seeding if this script is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      logger.info('Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Seeding failed:', error);
      process.exit(1);
    });
}

export { seedDatabase };
