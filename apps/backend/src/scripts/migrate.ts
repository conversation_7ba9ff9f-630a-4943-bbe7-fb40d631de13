import { readFileSync } from 'fs';
import { join } from 'path';
import { pgPool, logger } from '../config';

async function runMigrations() {
  const client = await pgPool.connect();
  
  try {
    logger.info('Starting database migrations...');
    
    // Create migrations table if it doesn't exist
    await client.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        filename VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Get list of executed migrations
    const executedMigrations = await client.query(
      'SELECT filename FROM migrations ORDER BY id'
    );
    const executedFiles = executedMigrations.rows.map(row => row.filename);
    
    // Migration files to run
    const migrationFiles = [
      '001_initial_schema.sql',
      '002_market_data_schema.sql',
    ];
    
    for (const filename of migrationFiles) {
      if (executedFiles.includes(filename)) {
        logger.info(`Migration ${filename} already executed, skipping...`);
        continue;
      }
      
      logger.info(`Running migration: ${filename}`);
      
      try {
        // Read migration file
        const migrationPath = join(__dirname, '../database/migrations', filename);
        const migrationSQL = readFileSync(migrationPath, 'utf8');
        
        // Execute migration in a transaction
        await client.query('BEGIN');
        await client.query(migrationSQL);
        await client.query(
          'INSERT INTO migrations (filename) VALUES ($1)',
          [filename]
        );
        await client.query('COMMIT');
        
        logger.info(`Migration ${filename} completed successfully`);
      } catch (error) {
        await client.query('ROLLBACK');
        logger.error(`Migration ${filename} failed:`, error);
        throw error;
      }
    }
    
    logger.info('All migrations completed successfully');
  } catch (error) {
    logger.error('Migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run migrations if this script is executed directly
if (require.main === module) {
  runMigrations()
    .then(() => {
      logger.info('Migrations completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Migration failed:', error);
      process.exit(1);
    });
}

export { runMigrations };
