import { FastifyPluginAsync } from 'fastify';
import fp from 'fastify-plugin';
import { SocketStream } from '@fastify/websocket';
import { WSMarketDataMessage, MarketTick } from '@trading-platform/shared';
import { MarketDataService } from '../services/MarketDataService';
import { logger } from '../config/logger';

interface WebSocketClient {
  id: string;
  userId?: string;
  socket: SocketStream;
  subscriptions: Set<string>;
  lastPing: number;
}

const websocketPlugin: FastifyPluginAsync = async (fastify) => {
  const clients = new Map<string, WebSocketClient>();
  let marketDataService: MarketDataService;

  // Generate unique client ID
  const generateClientId = (): string => {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  // Send message to client
  const sendToClient = (client: WebSocketClient, message: WSMarketDataMessage): void => {
    try {
      if (client.socket.readyState === client.socket.OPEN) {
        client.socket.send(JSON.stringify(message));
      }
    } catch (error) {
      logger.error('Failed to send message to client:', error);
    }
  };

  // Broadcast message to all subscribed clients
  const broadcastToSubscribers = (instrumentKey: string, message: WSMarketDataMessage): void => {
    clients.forEach(client => {
      if (client.subscriptions.has(instrumentKey)) {
        sendToClient(client, message);
      }
    });
  };

  // Handle market tick from market data service
  const handleMarketTick = (tick: MarketTick): void => {
    const message: WSMarketDataMessage = {
      type: 'tick',
      data: tick,
      timestamp: new Date(),
    };

    broadcastToSubscribers(tick.instrument_key, message);
  };

  // Setup market data service event handlers
  const setupMarketDataHandlers = (service: MarketDataService): void => {
    service.on('tick', handleMarketTick);
    
    service.on('connected', () => {
      const message: WSMarketDataMessage = {
        type: 'connection_status',
        data: { status: 'connected', message: 'Market data feed connected' },
        timestamp: new Date(),
      };
      
      clients.forEach(client => sendToClient(client, message));
    });

    service.on('disconnected', (info) => {
      const message: WSMarketDataMessage = {
        type: 'connection_status',
        data: { status: 'disconnected', message: 'Market data feed disconnected', info },
        timestamp: new Date(),
      };
      
      clients.forEach(client => sendToClient(client, message));
    });

    service.on('error', (error) => {
      const message: WSMarketDataMessage = {
        type: 'connection_status',
        data: { status: 'error', message: 'Market data feed error', error: error.message },
        timestamp: new Date(),
      };
      
      clients.forEach(client => sendToClient(client, message));
    });
  };

  // WebSocket route for market data
  fastify.register(async function (fastify) {
    fastify.get('/ws/market-data', { websocket: true }, (connection, request) => {
      const clientId = generateClientId();
      const client: WebSocketClient = {
        id: clientId,
        socket: connection.socket,
        subscriptions: new Set(),
        lastPing: Date.now(),
      };

      // Try to get user from token
      try {
        const authHeader = request.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
          const token = authHeader.substring(7);
          // Validate token and get user (simplified for this example)
          // In production, you'd validate the JWT token here
          client.userId = 'user_from_token';
        }
      } catch (error) {
        logger.warn('Failed to authenticate WebSocket client:', error);
      }

      clients.set(clientId, client);
      logger.info(`WebSocket client connected: ${clientId}`);

      // Send welcome message
      const welcomeMessage: WSMarketDataMessage = {
        type: 'connection_status',
        data: { 
          status: 'connected', 
          message: 'WebSocket connected successfully',
          clientId,
        },
        timestamp: new Date(),
      };
      sendToClient(client, welcomeMessage);

      // Handle incoming messages
      connection.socket.on('message', async (message: Buffer) => {
        try {
          const data = JSON.parse(message.toString());
          
          switch (data.type) {
            case 'subscribe':
              await handleSubscribe(client, data.instrument_keys || []);
              break;
              
            case 'unsubscribe':
              await handleUnsubscribe(client, data.instrument_keys || []);
              break;
              
            case 'ping':
              client.lastPing = Date.now();
              sendToClient(client, {
                type: 'connection_status',
                data: { status: 'pong' },
                timestamp: new Date(),
              });
              break;
              
            default:
              logger.warn(`Unknown message type: ${data.type}`);
          }
        } catch (error) {
          logger.error('Failed to handle WebSocket message:', error);
          sendToClient(client, {
            type: 'connection_status',
            data: { status: 'error', message: 'Invalid message format' },
            timestamp: new Date(),
          });
        }
      });

      // Handle client disconnect
      connection.socket.on('close', () => {
        logger.info(`WebSocket client disconnected: ${clientId}`);
        clients.delete(clientId);
      });

      connection.socket.on('error', (error) => {
        logger.error(`WebSocket client error: ${clientId}`, error);
        clients.delete(clientId);
      });
    });
  });

  // Handle subscription requests
  const handleSubscribe = async (client: WebSocketClient, instrumentKeys: string[]): Promise<void> => {
    try {
      if (!marketDataService) {
        throw new Error('Market data service not available');
      }

      // Add to client subscriptions
      instrumentKeys.forEach(key => client.subscriptions.add(key));

      // Subscribe to market data service if not already subscribed
      const subscriptions = instrumentKeys.map(key => ({
        instrument_key: key,
        mode: 'ltpc' as const,
      }));

      await marketDataService.subscribe(subscriptions);

      const message: WSMarketDataMessage = {
        type: 'subscription_success',
        data: { 
          message: `Subscribed to ${instrumentKeys.length} instruments`,
          instrument_keys: instrumentKeys,
        },
        timestamp: new Date(),
      };

      sendToClient(client, message);
      logger.info(`Client ${client.id} subscribed to ${instrumentKeys.length} instruments`);
    } catch (error) {
      logger.error('Failed to handle subscription:', error);
      
      const message: WSMarketDataMessage = {
        type: 'subscription_error',
        data: { 
          message: 'Failed to subscribe to instruments',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        timestamp: new Date(),
      };

      sendToClient(client, message);
    }
  };

  // Handle unsubscription requests
  const handleUnsubscribe = async (client: WebSocketClient, instrumentKeys: string[]): Promise<void> => {
    try {
      // Remove from client subscriptions
      instrumentKeys.forEach(key => client.subscriptions.delete(key));

      // Check if any other clients are still subscribed
      const stillSubscribed = instrumentKeys.filter(key => {
        return Array.from(clients.values()).some(c => 
          c.id !== client.id && c.subscriptions.has(key)
        );
      });

      // Unsubscribe from market data service if no other clients need it
      const toUnsubscribe = instrumentKeys.filter(key => !stillSubscribed.includes(key));
      
      if (toUnsubscribe.length > 0 && marketDataService) {
        await marketDataService.unsubscribe(toUnsubscribe);
      }

      const message: WSMarketDataMessage = {
        type: 'subscription_success',
        data: { 
          message: `Unsubscribed from ${instrumentKeys.length} instruments`,
          instrument_keys: instrumentKeys,
        },
        timestamp: new Date(),
      };

      sendToClient(client, message);
      logger.info(`Client ${client.id} unsubscribed from ${instrumentKeys.length} instruments`);
    } catch (error) {
      logger.error('Failed to handle unsubscription:', error);
      
      const message: WSMarketDataMessage = {
        type: 'subscription_error',
        data: { 
          message: 'Failed to unsubscribe from instruments',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        timestamp: new Date(),
      };

      sendToClient(client, message);
    }
  };

  // Heartbeat to check client connections
  const heartbeatInterval = setInterval(() => {
    const now = Date.now();
    const timeout = 60000; // 1 minute timeout

    clients.forEach((client, clientId) => {
      if (now - client.lastPing > timeout) {
        logger.info(`Removing inactive client: ${clientId}`);
        client.socket.close();
        clients.delete(clientId);
      }
    });
  }, 30000); // Check every 30 seconds

  // Initialize market data service when available
  fastify.addHook('onReady', async () => {
    if (fastify.marketDataService) {
      marketDataService = fastify.marketDataService;
      setupMarketDataHandlers(marketDataService);
      logger.info('WebSocket plugin connected to market data service');
    }
  });

  // Cleanup on server shutdown
  fastify.addHook('onClose', async () => {
    clearInterval(heartbeatInterval);
    clients.clear();
  });

  // Expose client management functions
  fastify.decorate('wsClients', {
    getClientCount: () => clients.size,
    getClients: () => Array.from(clients.values()),
    broadcastToAll: (message: WSMarketDataMessage) => {
      clients.forEach(client => sendToClient(client, message));
    },
  });
};

export default fp(websocketPlugin, {
  name: 'websocket',
  dependencies: ['websocket'],
});
