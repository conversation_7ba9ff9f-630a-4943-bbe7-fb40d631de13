import { FastifyPluginAsync, FastifyRequest } from 'fastify';
import fp from 'fastify-plugin';
import { User } from '@trading-platform/shared';
import { AuthService } from '../services/AuthService';
import { UserRepository } from '../database/repositories/UserRepository';
import { pgPool } from '../config/database';

declare module 'fastify' {
  interface FastifyRequest {
    user?: User;
  }
}

const authPlugin: FastifyPluginAsync = async (fastify) => {
  // Initialize services
  const userRepository = new UserRepository(pgPool);
  const authService = new AuthService(userRepository);

  // Register auth service in Fastify context
  fastify.decorate('authService', authService);

  // Authentication hook
  fastify.addHook('preHandler', async (request: FastifyRequest) => {
    // Skip auth for public routes
    const publicRoutes = [
      '/api/auth/login',
      '/api/auth/register',
      '/api/auth/reset-password',
      '/api/auth/confirm-reset',
      '/api/health',
      '/docs',
      '/documentation',
    ];

    const isPublicRoute = publicRoutes.some(route => 
      request.url.startsWith(route)
    );

    if (isPublicRoute) {
      return;
    }

    // Extract token from Authorization header
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw fastify.httpErrors.unauthorized('Missing or invalid authorization header');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    try {
      // Validate token and get user
      const user = await authService.validateToken(token);
      request.user = user;
    } catch (error) {
      throw fastify.httpErrors.unauthorized('Invalid or expired token');
    }
  });

  // Helper function to check user roles
  fastify.decorate('requireRole', (allowedRoles: string[]) => {
    return async (request: FastifyRequest) => {
      if (!request.user) {
        throw fastify.httpErrors.unauthorized('Authentication required');
      }

      if (!allowedRoles.includes(request.user.role)) {
        throw fastify.httpErrors.forbidden('Insufficient permissions');
      }
    };
  });

  // Helper function to check if user owns resource
  fastify.decorate('requireOwnership', (getUserId: (request: FastifyRequest) => string) => {
    return async (request: FastifyRequest) => {
      if (!request.user) {
        throw fastify.httpErrors.unauthorized('Authentication required');
      }

      const resourceUserId = getUserId(request);
      if (request.user.id !== resourceUserId && request.user.role !== 'admin') {
        throw fastify.httpErrors.forbidden('Access denied');
      }
    };
  });
};

export default fp(authPlugin, {
  name: 'auth',
});
