import WebSocket from 'ws';
import { EventEmitter } from 'events';
import { config } from '../config';
import { logger } from '../config/logger';
import { MarketTick, MarketDataSubscription } from '@trading-platform/shared';
import { UpstoxApiClient } from './UpstoxApiClient';

export interface UpstoxWebSocketConfig {
  accessToken: string;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
}

export interface UpstoxTickData {
  feeds: {
    [instrumentKey: string]: {
      ff: {
        marketFF: {
          ltpc: {
            ltp: number;
            ltt: number;
            ltq: number;
            cp: number;
          };
          marketOHLC: {
            ohlc: [number, number, number, number]; // [open, high, low, close]
          };
          marketLevel: {
            bidAskQuote: Array<{
              bq: number;
              bp: number;
              aq: number;
              ap: number;
            }>;
          };
          optionGreeks?: {
            vega: number;
            theta: number;
            gamma: number;
            delta: number;
            iv: number;
          };
        };
      };
    };
  };
}

export class UpstoxWebSocketClient extends EventEmitter {
  private ws: WebSocket | null = null;
  private config: UpstoxWebSocketConfig;
  private apiClient: UpstoxApiClient;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private isAuthenticated = false;
  private subscribedInstruments = new Set<string>();

  constructor(apiClient: UpstoxApiClient, wsConfig: Partial<UpstoxWebSocketConfig> = {}) {
    super();
    this.apiClient = apiClient;
    this.config = {
      accessToken: wsConfig.accessToken || config.upstoxAccessToken || '',
      reconnectInterval: wsConfig.reconnectInterval || 5000,
      maxReconnectAttempts: wsConfig.maxReconnectAttempts || 10,
      heartbeatInterval: wsConfig.heartbeatInterval || 30000,
    };
  }

  /**
   * Connect to Upstox WebSocket
   */
  async connect(): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      logger.warn('WebSocket connection already exists or is connecting');
      return;
    }

    try {
      this.isConnecting = true;
      logger.info('Connecting to Upstox WebSocket...');

      // Validate WebSocket authorization
      const authResult = await this.apiClient.getWebSocketAuth();
      if (!authResult.authorized) {
        throw new Error('WebSocket not authorized');
      }

      // Create WebSocket connection
      const wsUrl = `${config.upstoxWsUrl}?access_token=${this.config.accessToken}`;
      this.ws = new WebSocket(wsUrl, {
        headers: {
          'Authorization': `Bearer ${this.config.accessToken}`,
        },
      });

      this.setupWebSocketHandlers();
      
    } catch (error) {
      this.isConnecting = false;
      logger.error('Failed to connect to Upstox WebSocket:', error);
      this.emit('error', error);
      this.scheduleReconnect();
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupWebSocketHandlers(): void {
    if (!this.ws) return;

    this.ws.on('open', () => {
      logger.info('Upstox WebSocket connected successfully');
      this.isConnecting = false;
      this.isAuthenticated = true;
      this.reconnectAttempts = 0;
      this.startHeartbeat();
      this.emit('connected');
    });

    this.ws.on('message', (data: WebSocket.Data) => {
      try {
        this.handleMessage(data);
      } catch (error) {
        logger.error('Error handling WebSocket message:', error);
        this.emit('error', error);
      }
    });

    this.ws.on('close', (code: number, reason: Buffer) => {
      logger.warn(`Upstox WebSocket closed: ${code} - ${reason.toString()}`);
      this.isAuthenticated = false;
      this.stopHeartbeat();
      this.emit('disconnected', { code, reason: reason.toString() });
      this.scheduleReconnect();
    });

    this.ws.on('error', (error: Error) => {
      logger.error('Upstox WebSocket error:', error);
      this.isConnecting = false;
      this.emit('error', error);
    });

    this.ws.on('ping', () => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.pong();
      }
    });
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(data: WebSocket.Data): void {
    try {
      const message = JSON.parse(data.toString()) as UpstoxTickData;
      
      if (message.feeds) {
        Object.entries(message.feeds).forEach(([instrumentKey, feedData]) => {
          const tick = this.parseTickData(instrumentKey, feedData);
          if (tick) {
            this.emit('tick', tick);
          }
        });
      }
    } catch (error) {
      logger.error('Failed to parse WebSocket message:', error);
    }
  }

  /**
   * Parse tick data from Upstox format to our format
   */
  private parseTickData(instrumentKey: string, feedData: any): MarketTick | null {
    try {
      const marketFF = feedData.ff?.marketFF;
      if (!marketFF) return null;

      const ltpc = marketFF.ltpc;
      const ohlc = marketFF.marketOHLC?.ohlc;
      const bidAsk = marketFF.marketLevel?.bidAskQuote?.[0];

      const tick: MarketTick = {
        instrument_key: instrumentKey,
        exchange_token: instrumentKey.split('|')[1] || '',
        last_price: ltpc?.ltp || 0,
        volume: ltpc?.ltq || 0,
        average_price: 0, // Calculate if needed
        oi: 0, // Open interest for derivatives
        net_change: ltpc ? ltpc.ltp - ltpc.cp : 0,
        total_buy_quantity: bidAsk?.bq || 0,
        total_sell_quantity: bidAsk?.aq || 0,
        lower_circuit_limit: 0, // Not provided in this format
        upper_circuit_limit: 0, // Not provided in this format
        last_trade_time: ltpc?.ltt || Date.now(),
        oi_day_high: 0,
        oi_day_low: 0,
      };

      return tick;
    } catch (error) {
      logger.error('Failed to parse tick data:', error);
      return null;
    }
  }

  /**
   * Subscribe to market data for instruments
   */
  async subscribe(subscriptions: MarketDataSubscription[]): Promise<void> {
    if (!this.isAuthenticated || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
      logger.warn('WebSocket not connected, cannot subscribe');
      return;
    }

    try {
      const subscribeMessage = {
        guid: 'someguid',
        method: 'sub',
        data: {
          mode: subscriptions[0]?.mode || 'ltpc',
          instrumentKeys: subscriptions.map(sub => sub.instrument_key),
        },
      };

      this.ws.send(JSON.stringify(subscribeMessage));
      
      // Track subscribed instruments
      subscriptions.forEach(sub => {
        this.subscribedInstruments.add(sub.instrument_key);
      });

      logger.info(`Subscribed to ${subscriptions.length} instruments`);
      this.emit('subscribed', subscriptions);
    } catch (error) {
      logger.error('Failed to subscribe to instruments:', error);
      this.emit('error', error);
    }
  }

  /**
   * Unsubscribe from market data
   */
  async unsubscribe(instrumentKeys: string[]): Promise<void> {
    if (!this.isAuthenticated || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
      logger.warn('WebSocket not connected, cannot unsubscribe');
      return;
    }

    try {
      const unsubscribeMessage = {
        guid: 'someguid',
        method: 'unsub',
        data: {
          mode: 'ltpc',
          instrumentKeys,
        },
      };

      this.ws.send(JSON.stringify(unsubscribeMessage));
      
      // Remove from tracked instruments
      instrumentKeys.forEach(key => {
        this.subscribedInstruments.delete(key);
      });

      logger.info(`Unsubscribed from ${instrumentKeys.length} instruments`);
      this.emit('unsubscribed', instrumentKeys);
    } catch (error) {
      logger.error('Failed to unsubscribe from instruments:', error);
      this.emit('error', error);
    }
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.ping();
      }
    }, this.config.heartbeatInterval);
  }

  /**
   * Stop heartbeat timer
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts!) {
      logger.error('Max reconnection attempts reached, giving up');
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.reconnectAttempts++;
    const delay = this.config.reconnectInterval! * this.reconnectAttempts;
    
    logger.info(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    this.reconnectTimer = setTimeout(() => {
      this.connect();
    }, delay);
  }

  /**
   * Disconnect WebSocket
   */
  disconnect(): void {
    logger.info('Disconnecting Upstox WebSocket...');
    
    this.stopHeartbeat();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.isAuthenticated = false;
    this.subscribedInstruments.clear();
    this.emit('disconnected', { code: 1000, reason: 'Manual disconnect' });
  }

  /**
   * Get connection status
   */
  getStatus(): {
    connected: boolean;
    authenticated: boolean;
    subscribedCount: number;
    reconnectAttempts: number;
  } {
    return {
      connected: this.ws?.readyState === WebSocket.OPEN,
      authenticated: this.isAuthenticated,
      subscribedCount: this.subscribedInstruments.size,
      reconnectAttempts: this.reconnectAttempts,
    };
  }

  /**
   * Get subscribed instruments
   */
  getSubscribedInstruments(): string[] {
    return Array.from(this.subscribedInstruments);
  }
}
