import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { config } from '../config';
import { logger } from '../config/logger';
import { UpstoxInstrument } from '@trading-platform/shared';

export interface UpstoxAuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
  scope?: string;
}

export interface UpstoxProfile {
  user_id: string;
  user_name: string;
  email: string;
  user_type: string;
  broker: string;
  exchanges: string[];
  products: string[];
  order_types: string[];
}

export interface UpstoxInstrumentResponse {
  status: string;
  data: Record<string, UpstoxInstrument[]>;
}

export class UpstoxApiClient {
  private apiClient: AxiosInstance;
  private accessToken: string | null = null;

  constructor() {
    this.apiClient = axios.create({
      baseURL: config.upstoxApiBaseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Set initial access token if available
    if (config.upstoxAccessToken) {
      this.setAccessToken(config.upstoxAccessToken);
    }

    // Request interceptor to add auth token
    this.apiClient.interceptors.request.use(
      (config) => {
        if (this.accessToken) {
          config.headers.Authorization = `Bearer ${this.accessToken}`;
        }
        return config;
      },
      (error) => {
        logger.error('Upstox API request error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.apiClient.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        logger.error('Upstox API response error:', {
          status: error.response?.status,
          data: error.response?.data,
          url: error.config?.url,
        });
        return Promise.reject(error);
      }
    );
  }

  setAccessToken(token: string): void {
    this.accessToken = token;
  }

  /**
   * Get authorization URL for OAuth flow
   */
  getAuthUrl(state?: string): string {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: config.upstoxApiKey!,
      redirect_uri: config.upstoxRedirectUri!,
      state: state || 'default',
    });

    return `https://api.upstox.com/v2/login/authorization/dialog?${params.toString()}`;
  }

  /**
   * Exchange authorization code for access token
   */
  async getAccessToken(code: string): Promise<UpstoxAuthResponse> {
    try {
      const response = await this.apiClient.post('/login/authorization/token', {
        code,
        client_id: config.upstoxApiKey,
        client_secret: config.upstoxApiSecret,
        redirect_uri: config.upstoxRedirectUri,
        grant_type: 'authorization_code',
      });

      const authData = response.data;
      this.setAccessToken(authData.access_token);
      
      logger.info('Successfully obtained Upstox access token');
      return authData;
    } catch (error) {
      logger.error('Failed to get Upstox access token:', error);
      throw new Error('Failed to authenticate with Upstox');
    }
  }

  /**
   * Get user profile information
   */
  async getProfile(): Promise<UpstoxProfile> {
    try {
      const response = await this.apiClient.get('/user/profile');
      return response.data.data;
    } catch (error) {
      logger.error('Failed to get Upstox profile:', error);
      throw new Error('Failed to fetch user profile');
    }
  }

  /**
   * Get all instruments from Upstox
   */
  async getInstruments(): Promise<UpstoxInstrument[]> {
    try {
      logger.info('Fetching instruments from Upstox...');
      const response = await this.apiClient.get<UpstoxInstrumentResponse>('/market-quote/instruments');
      
      if (response.data.status !== 'success') {
        throw new Error('Failed to fetch instruments from Upstox');
      }

      // Flatten the instruments from all exchanges
      const allInstruments: UpstoxInstrument[] = [];
      Object.values(response.data.data).forEach(exchangeInstruments => {
        allInstruments.push(...exchangeInstruments);
      });

      logger.info(`Fetched ${allInstruments.length} instruments from Upstox`);
      return allInstruments;
    } catch (error) {
      logger.error('Failed to fetch instruments:', error);
      throw new Error('Failed to fetch instruments from Upstox');
    }
  }

  /**
   * Get market quote for specific instruments
   */
  async getQuotes(instrumentKeys: string[]): Promise<any> {
    try {
      const response = await this.apiClient.get('/market-quote/quotes', {
        params: {
          instrument_key: instrumentKeys.join(','),
        },
      });

      return response.data.data;
    } catch (error) {
      logger.error('Failed to get quotes:', error);
      throw new Error('Failed to fetch market quotes');
    }
  }

  /**
   * Get historical candle data
   */
  async getHistoricalData(
    instrumentKey: string,
    interval: string,
    toDate: string,
    fromDate?: string
  ): Promise<any> {
    try {
      const params: any = {
        instrument_key: instrumentKey,
        interval,
        to_date: toDate,
      };

      if (fromDate) {
        params.from_date = fromDate;
      }

      const response = await this.apiClient.get('/historical-candle/intraday', {
        params,
      });

      return response.data.data;
    } catch (error) {
      logger.error('Failed to get historical data:', error);
      throw new Error('Failed to fetch historical data');
    }
  }

  /**
   * Search instruments by symbol or name
   */
  async searchInstruments(query: string): Promise<UpstoxInstrument[]> {
    try {
      // Note: Upstox doesn't have a direct search API, so we'll need to implement this
      // by fetching all instruments and filtering locally, or use our database
      throw new Error('Search functionality should be implemented using local database');
    } catch (error) {
      logger.error('Failed to search instruments:', error);
      throw error;
    }
  }

  /**
   * Get market status
   */
  async getMarketStatus(): Promise<any> {
    try {
      const response = await this.apiClient.get('/market-quote/market-status');
      return response.data.data;
    } catch (error) {
      logger.error('Failed to get market status:', error);
      throw new Error('Failed to fetch market status');
    }
  }

  /**
   * Validate if the API client is properly authenticated
   */
  async validateAuth(): Promise<boolean> {
    try {
      await this.getProfile();
      return true;
    } catch (error) {
      logger.warn('Upstox authentication validation failed:', error);
      return false;
    }
  }

  /**
   * Get WebSocket authorization token
   */
  async getWebSocketAuth(): Promise<{ authorized: boolean; token?: string }> {
    try {
      const response = await this.apiClient.get('/feed/market-data-feed/authorize');
      return {
        authorized: response.data.data.authorized,
        token: this.accessToken || undefined,
      };
    } catch (error) {
      logger.error('Failed to get WebSocket authorization:', error);
      return { authorized: false };
    }
  }
}
