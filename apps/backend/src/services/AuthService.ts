import jwt from 'jsonwebtoken';
import { User, LoginSchema, RegisterSchema } from '@trading-platform/shared';
import { UserRepository } from '../database/repositories/UserRepository';
import { config } from '../config';
import { logger } from '../config/logger';

export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

export class AuthService {
  constructor(private userRepository: UserRepository) {}

  async register(data: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }): Promise<{ user: User; token: string }> {
    // Validate input
    const validatedData = RegisterSchema.parse(data);

    // Check if user already exists
    const existingUser = await this.userRepository.findByEmail(validatedData.email);
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Create user
    const user = await this.userRepository.create(validatedData);
    
    // Generate JWT token
    const token = this.generateToken(user);

    logger.info('User registered successfully', { userId: user.id, email: user.email });

    return { user, token };
  }

  async login(data: {
    email: string;
    password: string;
  }): Promise<{ user: User; token: string }> {
    // Validate input
    const validatedData = LoginSchema.parse(data);

    // Verify credentials
    const user = await this.userRepository.verifyPassword(
      validatedData.email,
      validatedData.password
    );

    if (!user) {
      throw new Error('Invalid email or password');
    }

    if (!user.isActive) {
      throw new Error('Account is deactivated');
    }

    // Generate JWT token
    const token = this.generateToken(user);

    logger.info('User logged in successfully', { userId: user.id, email: user.email });

    return { user, token };
  }

  async refreshToken(token: string): Promise<{ user: User; token: string }> {
    try {
      // Verify and decode the token
      const decoded = jwt.verify(token, config.jwtSecret) as JWTPayload;

      // Get user from database
      const user = await this.userRepository.findById(decoded.userId);
      if (!user || !user.isActive) {
        throw new Error('User not found or inactive');
      }

      // Generate new token
      const newToken = this.generateToken(user);

      return { user, token: newToken };
    } catch (error) {
      throw new Error('Invalid or expired token');
    }
  }

  async validateToken(token: string): Promise<User> {
    try {
      // Verify and decode the token
      const decoded = jwt.verify(token, config.jwtSecret) as JWTPayload;

      // Get user from database
      const user = await this.userRepository.findById(decoded.userId);
      if (!user || !user.isActive) {
        throw new Error('User not found or inactive');
      }

      return user;
    } catch (error) {
      throw new Error('Invalid or expired token');
    }
  }

  async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    // Get user with password
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Verify current password
    const userWithPassword = await this.userRepository.findByEmailWithPassword(user.email);
    if (!userWithPassword) {
      throw new Error('User not found');
    }

    const bcrypt = await import('bcrypt');
    const isCurrentPasswordValid = await bcrypt.compare(
      currentPassword,
      userWithPassword.passwordHash
    );

    if (!isCurrentPasswordValid) {
      throw new Error('Current password is incorrect');
    }

    // Validate new password
    if (newPassword.length < 8) {
      throw new Error('New password must be at least 8 characters long');
    }

    // Update password
    await this.userRepository.updatePassword(userId, newPassword);

    logger.info('Password changed successfully', { userId });
  }

  async resetPassword(email: string): Promise<void> {
    const user = await this.userRepository.findByEmail(email);
    if (!user) {
      // Don't reveal if email exists or not
      logger.warn('Password reset attempted for non-existent email', { email });
      return;
    }

    // In a real application, you would:
    // 1. Generate a secure reset token
    // 2. Store it in the database with expiration
    // 3. Send email with reset link
    // For now, we'll just log it

    const resetToken = jwt.sign(
      { userId: user.id, type: 'password_reset' },
      config.jwtSecret,
      { expiresIn: '1h' }
    );

    logger.info('Password reset token generated', { 
      userId: user.id, 
      email: user.email,
      resetToken // In production, don't log the actual token
    });

    // TODO: Send email with reset link
  }

  async confirmPasswordReset(token: string, newPassword: string): Promise<void> {
    try {
      const decoded = jwt.verify(token, config.jwtSecret) as any;
      
      if (decoded.type !== 'password_reset') {
        throw new Error('Invalid token type');
      }

      // Validate new password
      if (newPassword.length < 8) {
        throw new Error('Password must be at least 8 characters long');
      }

      // Update password
      await this.userRepository.updatePassword(decoded.userId, newPassword);

      logger.info('Password reset completed', { userId: decoded.userId });
    } catch (error) {
      throw new Error('Invalid or expired reset token');
    }
  }

  private generateToken(user: User): string {
    const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
      userId: user.id,
      email: user.email,
      role: user.role,
    };

    return jwt.sign(payload, config.jwtSecret, {
      expiresIn: config.jwtExpiresIn,
    });
  }

  async getUserFromToken(token: string): Promise<User | null> {
    try {
      return await this.validateToken(token);
    } catch (error) {
      return null;
    }
  }
}
