import { EventEmitter } from 'events';
import { MarketDataService } from './MarketDataService';
import { UpstoxApiClient } from './UpstoxApiClient';
import { checkDatabaseHealth } from '../config/database';
import { logger } from '../config/logger';

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  services: {
    database: {
      postgres: boolean;
      redis: boolean;
      influx: boolean;
    };
    upstox: {
      api: boolean;
      websocket: boolean;
    };
    marketData: {
      initialized: boolean;
      connected: boolean;
      subscribedCount: number;
    };
  };
  errors: string[];
}

export class HealthCheckService extends EventEmitter {
  private marketDataService: MarketDataService | null = null;
  private upstoxApi: UpstoxApiClient | null = null;
  private checkInterval: NodeJS.Timeout | null = null;
  private lastHealthStatus: HealthStatus | null = null;

  constructor() {
    super();
  }

  /**
   * Initialize health check service
   */
  initialize(marketDataService: MarketDataService, upstoxApi: UpstoxApiClient): void {
    this.marketDataService = marketDataService;
    this.upstoxApi = upstoxApi;
    
    // Start periodic health checks
    this.startPeriodicChecks();
    
    logger.info('Health check service initialized');
  }

  /**
   * Start periodic health checks
   */
  private startPeriodicChecks(): void {
    // Check every 30 seconds
    this.checkInterval = setInterval(async () => {
      try {
        const health = await this.checkHealth();
        this.lastHealthStatus = health;
        
        // Emit health status change events
        this.emit('healthCheck', health);
        
        if (health.status === 'unhealthy') {
          this.emit('unhealthy', health);
          logger.warn('System health check failed:', health.errors);
        } else if (health.status === 'degraded') {
          this.emit('degraded', health);
          logger.warn('System health degraded:', health.errors);
        }
        
      } catch (error) {
        logger.error('Health check failed:', error);
        this.emit('error', error);
      }
    }, 30000);
  }

  /**
   * Perform comprehensive health check
   */
  async checkHealth(): Promise<HealthStatus> {
    const errors: string[] = [];
    const timestamp = new Date();

    // Check database health
    const dbHealth = await this.checkDatabaseHealth();
    if (!dbHealth.postgres) errors.push('PostgreSQL connection failed');
    if (!dbHealth.redis) errors.push('Redis connection failed');
    if (!dbHealth.influx) errors.push('InfluxDB connection failed');

    // Check Upstox API health
    const upstoxHealth = await this.checkUpstoxHealth();
    if (!upstoxHealth.api) errors.push('Upstox API connection failed');
    if (!upstoxHealth.websocket) errors.push('Upstox WebSocket connection failed');

    // Check market data service health
    const marketDataHealth = this.checkMarketDataHealth();
    if (!marketDataHealth.initialized) errors.push('Market data service not initialized');
    if (!marketDataHealth.connected) errors.push('Market data WebSocket not connected');

    // Determine overall status
    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (errors.length === 0) {
      status = 'healthy';
    } else if (this.isCriticalError(errors)) {
      status = 'unhealthy';
    } else {
      status = 'degraded';
    }

    return {
      status,
      timestamp,
      services: {
        database: dbHealth,
        upstox: upstoxHealth,
        marketData: marketDataHealth,
      },
      errors,
    };
  }

  /**
   * Check database health
   */
  private async checkDatabaseHealth(): Promise<{
    postgres: boolean;
    redis: boolean;
    influx: boolean;
  }> {
    try {
      return await checkDatabaseHealth();
    } catch (error) {
      logger.error('Database health check failed:', error);
      return {
        postgres: false,
        redis: false,
        influx: false,
      };
    }
  }

  /**
   * Check Upstox API health
   */
  private async checkUpstoxHealth(): Promise<{
    api: boolean;
    websocket: boolean;
  }> {
    let apiHealth = false;
    let websocketHealth = false;

    try {
      if (this.upstoxApi) {
        apiHealth = await this.upstoxApi.validateAuth();
      }
    } catch (error) {
      logger.error('Upstox API health check failed:', error);
    }

    try {
      if (this.marketDataService) {
        const status = this.marketDataService.getStatus();
        websocketHealth = status.connected;
      }
    } catch (error) {
      logger.error('Upstox WebSocket health check failed:', error);
    }

    return {
      api: apiHealth,
      websocket: websocketHealth,
    };
  }

  /**
   * Check market data service health
   */
  private checkMarketDataHealth(): {
    initialized: boolean;
    connected: boolean;
    subscribedCount: number;
  } {
    if (!this.marketDataService) {
      return {
        initialized: false,
        connected: false,
        subscribedCount: 0,
      };
    }

    const status = this.marketDataService.getStatus();
    return {
      initialized: status.initialized,
      connected: status.connected,
      subscribedCount: status.subscribedCount,
    };
  }

  /**
   * Determine if errors are critical
   */
  private isCriticalError(errors: string[]): boolean {
    const criticalErrors = [
      'PostgreSQL connection failed',
      'Market data service not initialized',
      'Upstox API connection failed',
    ];

    return errors.some(error => 
      criticalErrors.some(critical => error.includes(critical))
    );
  }

  /**
   * Get last health status
   */
  getLastHealthStatus(): HealthStatus | null {
    return this.lastHealthStatus;
  }

  /**
   * Force a health check
   */
  async forceHealthCheck(): Promise<HealthStatus> {
    const health = await this.checkHealth();
    this.lastHealthStatus = health;
    this.emit('healthCheck', health);
    return health;
  }

  /**
   * Get health metrics for monitoring
   */
  getHealthMetrics(): {
    uptime: number;
    lastCheckTime: Date | null;
    totalChecks: number;
    failedChecks: number;
    status: string;
  } {
    return {
      uptime: process.uptime(),
      lastCheckTime: this.lastHealthStatus?.timestamp || null,
      totalChecks: 0, // TODO: Implement counter
      failedChecks: 0, // TODO: Implement counter
      status: this.lastHealthStatus?.status || 'unknown',
    };
  }

  /**
   * Stop health checks
   */
  stop(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
    
    logger.info('Health check service stopped');
  }

  /**
   * Cleanup
   */
  cleanup(): void {
    this.stop();
    this.removeAllListeners();
    this.marketDataService = null;
    this.upstoxApi = null;
    this.lastHealthStatus = null;
  }
}
