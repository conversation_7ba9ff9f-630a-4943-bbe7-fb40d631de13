import { EventEmitter } from 'events';
import { Point } from '@influxdata/influxdb-client';
import { MarketTick, MarketDataSubscription } from '@trading-platform/shared';
import { UpstoxApiClient } from './UpstoxApiClient';
import { UpstoxWebSocketClient } from './UpstoxWebSocketClient';
import { InstrumentRepository } from '../database/repositories/InstrumentRepository';
import { influxWriteApi, redisClient } from '../config/database';
import { logger } from '../config/logger';

export interface MarketDataCache {
  [instrumentKey: string]: {
    lastPrice: number;
    volume: number;
    netChange: number;
    percentageChange: number;
    lastUpdateTime: number;
  };
}

export class MarketDataService extends EventEmitter {
  private upstoxApi: UpstoxApiClient;
  private upstoxWs: UpstoxWebSocketClient;
  private instrumentRepo: InstrumentRepository;
  private marketDataCache: MarketDataCache = {};
  private activeSubscriptions = new Map<string, MarketDataSubscription>();
  private isInitialized = false;

  constructor(instrumentRepo: InstrumentRepository) {
    super();
    this.instrumentRepo = instrumentRepo;
    this.upstoxApi = new UpstoxApiClient();
    this.upstoxWs = new UpstoxWebSocketClient(this.upstoxApi);
    
    this.setupWebSocketHandlers();
  }

  /**
   * Initialize the market data service
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Market Data Service...');
      
      // Validate Upstox authentication
      const isAuthenticated = await this.upstoxApi.validateAuth();
      if (!isAuthenticated) {
        throw new Error('Upstox API authentication failed');
      }

      // Connect to WebSocket
      await this.upstoxWs.connect();
      
      // Load market data cache from Redis
      await this.loadMarketDataCache();
      
      this.isInitialized = true;
      logger.info('Market Data Service initialized successfully');
      this.emit('initialized');
      
    } catch (error) {
      logger.error('Failed to initialize Market Data Service:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupWebSocketHandlers(): void {
    this.upstoxWs.on('connected', () => {
      logger.info('Market data WebSocket connected');
      this.emit('connected');
    });

    this.upstoxWs.on('disconnected', (info) => {
      logger.warn('Market data WebSocket disconnected:', info);
      this.emit('disconnected', info);
    });

    this.upstoxWs.on('tick', (tick: MarketTick) => {
      this.handleMarketTick(tick);
    });

    this.upstoxWs.on('error', (error) => {
      logger.error('Market data WebSocket error:', error);
      this.emit('error', error);
    });

    this.upstoxWs.on('subscribed', (subscriptions) => {
      logger.info(`Subscribed to ${subscriptions.length} instruments`);
      this.emit('subscribed', subscriptions);
    });

    this.upstoxWs.on('unsubscribed', (instrumentKeys) => {
      logger.info(`Unsubscribed from ${instrumentKeys.length} instruments`);
      this.emit('unsubscribed', instrumentKeys);
    });
  }

  /**
   * Handle incoming market tick data
   */
  private async handleMarketTick(tick: MarketTick): Promise<void> {
    try {
      // Update local cache
      this.updateMarketDataCache(tick);
      
      // Store in InfluxDB for historical analysis
      await this.storeTickInInfluxDB(tick);
      
      // Update PostgreSQL cache table
      await this.updatePostgreSQLCache(tick);
      
      // Emit tick event for real-time subscribers
      this.emit('tick', tick);
      
    } catch (error) {
      logger.error('Failed to handle market tick:', error);
    }
  }

  /**
   * Update local market data cache
   */
  private updateMarketDataCache(tick: MarketTick): void {
    const previousData = this.marketDataCache[tick.instrument_key];
    const percentageChange = previousData 
      ? ((tick.last_price - previousData.lastPrice) / previousData.lastPrice) * 100
      : 0;

    this.marketDataCache[tick.instrument_key] = {
      lastPrice: tick.last_price,
      volume: tick.volume,
      netChange: tick.net_change,
      percentageChange,
      lastUpdateTime: Date.now(),
    };

    // Update Redis cache asynchronously
    this.updateRedisCache(tick.instrument_key, this.marketDataCache[tick.instrument_key])
      .catch(error => logger.error('Failed to update Redis cache:', error));
  }

  /**
   * Store tick data in InfluxDB
   */
  private async storeTickInInfluxDB(tick: MarketTick): Promise<void> {
    try {
      const point = new Point('market_tick')
        .tag('instrument_key', tick.instrument_key)
        .tag('exchange_token', tick.exchange_token)
        .floatField('last_price', tick.last_price)
        .intField('volume', tick.volume)
        .floatField('average_price', tick.average_price)
        .floatField('net_change', tick.net_change)
        .intField('total_buy_quantity', tick.total_buy_quantity)
        .intField('total_sell_quantity', tick.total_sell_quantity)
        .timestamp(new Date(tick.last_trade_time));

      if (tick.oi !== undefined) {
        point.intField('open_interest', tick.oi);
      }

      influxWriteApi.writePoint(point);
      
      // Flush every 100 points or every 5 seconds
      if (Math.random() < 0.01) { // 1% chance to flush
        await influxWriteApi.flush();
      }
      
    } catch (error) {
      logger.error('Failed to store tick in InfluxDB:', error);
    }
  }

  /**
   * Update PostgreSQL market data cache
   */
  private async updatePostgreSQLCache(tick: MarketTick): Promise<void> {
    try {
      const cacheData = this.marketDataCache[tick.instrument_key];
      
      const query = `
        INSERT INTO market_data_cache (
          instrument_key, last_price, volume, average_price, net_change,
          percentage_change, total_buy_quantity, total_sell_quantity,
          lower_circuit_limit, upper_circuit_limit, last_trade_time, open_interest
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        ON CONFLICT (instrument_key) 
        DO UPDATE SET
          last_price = EXCLUDED.last_price,
          volume = EXCLUDED.volume,
          average_price = EXCLUDED.average_price,
          net_change = EXCLUDED.net_change,
          percentage_change = EXCLUDED.percentage_change,
          total_buy_quantity = EXCLUDED.total_buy_quantity,
          total_sell_quantity = EXCLUDED.total_sell_quantity,
          lower_circuit_limit = EXCLUDED.lower_circuit_limit,
          upper_circuit_limit = EXCLUDED.upper_circuit_limit,
          last_trade_time = EXCLUDED.last_trade_time,
          open_interest = EXCLUDED.open_interest,
          updated_at = CURRENT_TIMESTAMP
      `;

      const values = [
        tick.instrument_key,
        tick.last_price,
        tick.volume,
        tick.average_price,
        tick.net_change,
        cacheData.percentageChange,
        tick.total_buy_quantity,
        tick.total_sell_quantity,
        tick.lower_circuit_limit,
        tick.upper_circuit_limit,
        tick.last_trade_time,
        tick.oi || 0,
      ];

      // Execute asynchronously to avoid blocking
      setImmediate(async () => {
        try {
          await this.instrumentRepo.pool.query(query, values);
        } catch (error) {
          logger.error('Failed to update PostgreSQL cache:', error);
        }
      });
      
    } catch (error) {
      logger.error('Failed to prepare PostgreSQL cache update:', error);
    }
  }

  /**
   * Update Redis cache
   */
  private async updateRedisCache(instrumentKey: string, data: any): Promise<void> {
    try {
      await redisClient.setEx(
        `market:${instrumentKey}`,
        300, // 5 minutes TTL
        JSON.stringify(data)
      );
    } catch (error) {
      logger.error('Failed to update Redis cache:', error);
    }
  }

  /**
   * Load market data cache from Redis
   */
  private async loadMarketDataCache(): Promise<void> {
    try {
      const keys = await redisClient.keys('market:*');
      const pipeline = redisClient.multi();
      
      keys.forEach(key => {
        pipeline.get(key);
      });
      
      const results = await pipeline.exec();
      
      if (results) {
        keys.forEach((key, index) => {
          const instrumentKey = key.replace('market:', '');
          const data = results[index];
          
          if (data && typeof data === 'string') {
            try {
              this.marketDataCache[instrumentKey] = JSON.parse(data);
            } catch (error) {
              logger.warn(`Failed to parse cached data for ${instrumentKey}`);
            }
          }
        });
      }
      
      logger.info(`Loaded ${Object.keys(this.marketDataCache).length} instruments from cache`);
    } catch (error) {
      logger.error('Failed to load market data cache:', error);
    }
  }

  /**
   * Subscribe to market data for instruments
   */
  async subscribe(subscriptions: MarketDataSubscription[]): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Market Data Service not initialized');
    }

    try {
      // Validate instruments exist
      const instrumentKeys = subscriptions.map(sub => sub.instrument_key);
      const instruments = await this.instrumentRepo.getByInstrumentKeys(instrumentKeys);
      
      if (instruments.length !== instrumentKeys.length) {
        const foundKeys = instruments.map(inst => inst.instrument_key);
        const missingKeys = instrumentKeys.filter(key => !foundKeys.includes(key));
        logger.warn(`Some instruments not found: ${missingKeys.join(', ')}`);
      }

      // Subscribe via WebSocket
      await this.upstoxWs.subscribe(subscriptions);
      
      // Track active subscriptions
      subscriptions.forEach(sub => {
        this.activeSubscriptions.set(sub.instrument_key, sub);
      });
      
      logger.info(`Subscribed to ${subscriptions.length} instruments`);
    } catch (error) {
      logger.error('Failed to subscribe to market data:', error);
      throw error;
    }
  }

  /**
   * Unsubscribe from market data
   */
  async unsubscribe(instrumentKeys: string[]): Promise<void> {
    try {
      await this.upstoxWs.unsubscribe(instrumentKeys);
      
      // Remove from active subscriptions
      instrumentKeys.forEach(key => {
        this.activeSubscriptions.delete(key);
      });
      
      logger.info(`Unsubscribed from ${instrumentKeys.length} instruments`);
    } catch (error) {
      logger.error('Failed to unsubscribe from market data:', error);
      throw error;
    }
  }

  /**
   * Get current market data for instruments
   */
  async getCurrentData(instrumentKeys: string[]): Promise<MarketDataCache> {
    const result: MarketDataCache = {};
    
    for (const key of instrumentKeys) {
      if (this.marketDataCache[key]) {
        result[key] = this.marketDataCache[key];
      } else {
        // Try to get from Redis cache
        try {
          const cached = await redisClient.get(`market:${key}`);
          if (cached) {
            result[key] = JSON.parse(cached);
          }
        } catch (error) {
          logger.warn(`Failed to get cached data for ${key}`);
        }
      }
    }
    
    return result;
  }

  /**
   * Get service status
   */
  getStatus(): {
    initialized: boolean;
    connected: boolean;
    subscribedCount: number;
    cacheSize: number;
    wsStatus: any;
  } {
    return {
      initialized: this.isInitialized,
      connected: this.upstoxWs.getStatus().connected,
      subscribedCount: this.activeSubscriptions.size,
      cacheSize: Object.keys(this.marketDataCache).length,
      wsStatus: this.upstoxWs.getStatus(),
    };
  }

  /**
   * Cleanup and disconnect
   */
  async cleanup(): Promise<void> {
    try {
      logger.info('Cleaning up Market Data Service...');
      
      // Flush any pending InfluxDB writes
      await influxWriteApi.flush();
      
      // Disconnect WebSocket
      this.upstoxWs.disconnect();
      
      // Clear caches
      this.marketDataCache = {};
      this.activeSubscriptions.clear();
      
      this.isInitialized = false;
      logger.info('Market Data Service cleanup completed');
      
    } catch (error) {
      logger.error('Error during Market Data Service cleanup:', error);
    }
  }
}
