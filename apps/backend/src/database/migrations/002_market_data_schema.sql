-- Market Data and Instruments Schema

-- Instruments table for storing Upstox instrument data
CREATE TABLE instruments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instrument_key VARCHAR(50) UNIQUE NOT NULL,
    exchange_token VARCHAR(50) NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    name VA<PERSON><PERSON>R(255) NOT NULL,
    exchange VARCHAR(20) NOT NULL,
    segment VARCHAR(20) NOT NULL,
    instrument_type VARCHAR(20) NOT NULL,
    tick_size DECIMAL(10, 4) NOT NULL DEFAULT 0.05,
    lot_size INTEGER NOT NULL DEFAULT 1,
    expiry DATE,
    strike DECIMAL(10, 2),
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Watchlists table for user-defined instrument lists
CREATE TABLE watchlists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    instruments TEXT[] DEFAULT '{}', -- Array of instrument_keys
    is_default BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, name)
);

-- Market data subscriptions table
CREATE TABLE market_data_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    instrument_key VARCHAR(50) NOT NULL,
    mode VARCHAR(10) NOT NULL DEFAULT 'ltpc' CHECK (mode IN ('ltpc', 'full')),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, instrument_key)
);

-- Market data cache table for latest prices
CREATE TABLE market_data_cache (
    instrument_key VARCHAR(50) PRIMARY KEY,
    last_price DECIMAL(15, 4) NOT NULL,
    volume BIGINT NOT NULL DEFAULT 0,
    average_price DECIMAL(15, 4),
    net_change DECIMAL(15, 4) NOT NULL DEFAULT 0,
    percentage_change DECIMAL(8, 4) NOT NULL DEFAULT 0,
    total_buy_quantity BIGINT DEFAULT 0,
    total_sell_quantity BIGINT DEFAULT 0,
    lower_circuit_limit DECIMAL(15, 4),
    upper_circuit_limit DECIMAL(15, 4),
    last_trade_time BIGINT,
    open_interest BIGINT DEFAULT 0,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Upstox API tokens table for managing authentication
CREATE TABLE upstox_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    token_type VARCHAR(20) DEFAULT 'Bearer',
    expires_at TIMESTAMP WITH TIME ZONE,
    scope TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_instruments_symbol ON instruments(symbol);
CREATE INDEX idx_instruments_exchange ON instruments(exchange);
CREATE INDEX idx_instruments_instrument_key ON instruments(instrument_key);
CREATE INDEX idx_instruments_active ON instruments(is_active);
CREATE INDEX idx_instruments_segment ON instruments(segment);

CREATE INDEX idx_watchlists_user_id ON watchlists(user_id);
CREATE INDEX idx_watchlists_default ON watchlists(is_default);

CREATE INDEX idx_subscriptions_user_id ON market_data_subscriptions(user_id);
CREATE INDEX idx_subscriptions_instrument_key ON market_data_subscriptions(instrument_key);
CREATE INDEX idx_subscriptions_active ON market_data_subscriptions(is_active);

CREATE INDEX idx_market_cache_updated ON market_data_cache(updated_at);

CREATE INDEX idx_upstox_tokens_user_id ON upstox_tokens(user_id);
CREATE INDEX idx_upstox_tokens_active ON upstox_tokens(is_active);

-- Add updated_at triggers
CREATE TRIGGER update_instruments_updated_at BEFORE UPDATE ON instruments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_watchlists_updated_at BEFORE UPDATE ON watchlists FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON market_data_subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_upstox_tokens_updated_at BEFORE UPDATE ON upstox_tokens FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update market data cache timestamp
CREATE OR REPLACE FUNCTION update_market_cache_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_market_cache_updated_at BEFORE UPDATE ON market_data_cache FOR EACH ROW EXECUTE FUNCTION update_market_cache_timestamp();

-- Create a view for active instruments with latest market data
CREATE VIEW active_instruments_with_data AS
SELECT 
    i.id,
    i.instrument_key,
    i.exchange_token,
    i.symbol,
    i.name,
    i.exchange,
    i.segment,
    i.instrument_type,
    i.tick_size,
    i.lot_size,
    i.expiry,
    i.strike,
    mdc.last_price,
    mdc.volume,
    mdc.net_change,
    mdc.percentage_change,
    mdc.last_trade_time,
    mdc.updated_at as price_updated_at
FROM instruments i
LEFT JOIN market_data_cache mdc ON i.instrument_key = mdc.instrument_key
WHERE i.is_active = true;

-- Create a function to search instruments
CREATE OR REPLACE FUNCTION search_instruments(search_term TEXT, limit_count INTEGER DEFAULT 20)
RETURNS TABLE (
    id UUID,
    instrument_key VARCHAR(50),
    symbol VARCHAR(50),
    name VARCHAR(255),
    exchange VARCHAR(20),
    segment VARCHAR(20),
    instrument_type VARCHAR(20),
    last_price DECIMAL(15, 4)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        i.id,
        i.instrument_key,
        i.symbol,
        i.name,
        i.exchange,
        i.segment,
        i.instrument_type,
        mdc.last_price
    FROM instruments i
    LEFT JOIN market_data_cache mdc ON i.instrument_key = mdc.instrument_key
    WHERE i.is_active = true
    AND (
        i.symbol ILIKE '%' || search_term || '%' OR
        i.name ILIKE '%' || search_term || '%'
    )
    ORDER BY 
        CASE 
            WHEN i.symbol ILIKE search_term || '%' THEN 1
            WHEN i.symbol ILIKE '%' || search_term || '%' THEN 2
            WHEN i.name ILIKE search_term || '%' THEN 3
            ELSE 4
        END,
        i.symbol
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;
