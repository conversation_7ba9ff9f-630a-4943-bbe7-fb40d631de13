import { Pool } from 'pg';
import { Instrument, UpstoxInstrument } from '@trading-platform/shared';
import { logger } from '../../config/logger';

export class InstrumentRepository {
  constructor(private pool: Pool) {}

  /**
   * Bulk insert or update instruments from Upstox
   */
  async bulkUpsertInstruments(upstoxInstruments: UpstoxInstrument[]): Promise<number> {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      
      let insertedCount = 0;
      const batchSize = 1000;
      
      for (let i = 0; i < upstoxInstruments.length; i += batchSize) {
        const batch = upstoxInstruments.slice(i, i + batchSize);
        
        const values: any[] = [];
        const placeholders: string[] = [];
        
        batch.forEach((instrument, index) => {
          const baseIndex = index * 11;
          placeholders.push(
            `($${baseIndex + 1}, $${baseIndex + 2}, $${baseIndex + 3}, $${baseIndex + 4}, $${baseIndex + 5}, $${baseIndex + 6}, $${baseIndex + 7}, $${baseIndex + 8}, $${baseIndex + 9}, $${baseIndex + 10}, $${baseIndex + 11})`
          );
          
          values.push(
            instrument.instrument_key,
            instrument.exchange_token,
            instrument.tradingsymbol,
            instrument.name,
            instrument.segment,
            instrument.exchange,
            instrument.instrument_type,
            instrument.tick_size,
            instrument.lot_size,
            instrument.expiry ? new Date(instrument.expiry) : null,
            instrument.strike || null
          );
        });
        
        const query = `
          INSERT INTO instruments (
            instrument_key, exchange_token, symbol, name, segment, exchange, 
            instrument_type, tick_size, lot_size, expiry, strike
          ) VALUES ${placeholders.join(', ')}
          ON CONFLICT (instrument_key) 
          DO UPDATE SET
            exchange_token = EXCLUDED.exchange_token,
            symbol = EXCLUDED.symbol,
            name = EXCLUDED.name,
            segment = EXCLUDED.segment,
            exchange = EXCLUDED.exchange,
            instrument_type = EXCLUDED.instrument_type,
            tick_size = EXCLUDED.tick_size,
            lot_size = EXCLUDED.lot_size,
            expiry = EXCLUDED.expiry,
            strike = EXCLUDED.strike,
            last_updated = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        `;
        
        await client.query(query, values);
        insertedCount += batch.length;
        
        if (i % (batchSize * 5) === 0) {
          logger.info(`Processed ${i + batch.length} instruments...`);
        }
      }
      
      await client.query('COMMIT');
      logger.info(`Successfully upserted ${insertedCount} instruments`);
      return insertedCount;
      
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Failed to bulk upsert instruments:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Search instruments by symbol or name
   */
  async searchInstruments(
    query: string, 
    limit = 20, 
    exchanges?: string[], 
    segments?: string[]
  ): Promise<Instrument[]> {
    try {
      let whereConditions = ['i.is_active = true'];
      const values: any[] = [query, limit];
      let paramCount = 2;

      if (exchanges && exchanges.length > 0) {
        whereConditions.push(`i.exchange = ANY($${++paramCount})`);
        values.push(exchanges);
      }

      if (segments && segments.length > 0) {
        whereConditions.push(`i.segment = ANY($${++paramCount})`);
        values.push(segments);
      }

      const searchQuery = `
        SELECT 
          i.id, i.instrument_key, i.exchange_token, i.symbol, i.name,
          i.exchange, i.segment, i.instrument_type, i.tick_size, i.lot_size,
          i.expiry, i.strike, i.is_active, i.created_at, i.updated_at
        FROM instruments i
        WHERE ${whereConditions.join(' AND ')}
        AND (
          i.symbol ILIKE '%' || $1 || '%' OR
          i.name ILIKE '%' || $1 || '%'
        )
        ORDER BY 
          CASE 
            WHEN i.symbol ILIKE $1 || '%' THEN 1
            WHEN i.symbol ILIKE '%' || $1 || '%' THEN 2
            WHEN i.name ILIKE $1 || '%' THEN 3
            ELSE 4
          END,
          i.symbol
        LIMIT $2
      `;

      const result = await this.pool.query(searchQuery, values);
      return result.rows.map(row => this.mapRowToInstrument(row));
    } catch (error) {
      logger.error('Failed to search instruments:', error);
      throw error;
    }
  }

  /**
   * Get instrument by instrument key
   */
  async getByInstrumentKey(instrumentKey: string): Promise<Instrument | null> {
    try {
      const query = `
        SELECT 
          id, instrument_key, exchange_token, symbol, name, exchange, segment,
          instrument_type, tick_size, lot_size, expiry, strike, is_active,
          created_at, updated_at
        FROM instruments
        WHERE instrument_key = $1 AND is_active = true
      `;

      const result = await this.pool.query(query, [instrumentKey]);
      return result.rows[0] ? this.mapRowToInstrument(result.rows[0]) : null;
    } catch (error) {
      logger.error('Failed to get instrument by key:', error);
      throw error;
    }
  }

  /**
   * Get multiple instruments by keys
   */
  async getByInstrumentKeys(instrumentKeys: string[]): Promise<Instrument[]> {
    try {
      const query = `
        SELECT 
          id, instrument_key, exchange_token, symbol, name, exchange, segment,
          instrument_type, tick_size, lot_size, expiry, strike, is_active,
          created_at, updated_at
        FROM instruments
        WHERE instrument_key = ANY($1) AND is_active = true
        ORDER BY symbol
      `;

      const result = await this.pool.query(query, [instrumentKeys]);
      return result.rows.map(row => this.mapRowToInstrument(row));
    } catch (error) {
      logger.error('Failed to get instruments by keys:', error);
      throw error;
    }
  }

  /**
   * Get popular instruments (most traded)
   */
  async getPopularInstruments(limit = 50): Promise<Instrument[]> {
    try {
      const query = `
        SELECT 
          i.id, i.instrument_key, i.exchange_token, i.symbol, i.name,
          i.exchange, i.segment, i.instrument_type, i.tick_size, i.lot_size,
          i.expiry, i.strike, i.is_active, i.created_at, i.updated_at
        FROM instruments i
        WHERE i.is_active = true 
        AND i.segment IN ('NSE_EQ', 'BSE_EQ')
        AND i.instrument_type = 'EQ'
        ORDER BY 
          CASE 
            WHEN i.symbol IN ('RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'HINDUNILVR', 'ICICIBANK', 'KOTAKBANK', 'BHARTIARTL', 'ITC', 'SBIN') THEN 1
            ELSE 2
          END,
          i.symbol
        LIMIT $1
      `;

      const result = await this.pool.query(query, [limit]);
      return result.rows.map(row => this.mapRowToInstrument(row));
    } catch (error) {
      logger.error('Failed to get popular instruments:', error);
      throw error;
    }
  }

  /**
   * Get instruments by exchange
   */
  async getByExchange(exchange: string, limit = 100): Promise<Instrument[]> {
    try {
      const query = `
        SELECT 
          id, instrument_key, exchange_token, symbol, name, exchange, segment,
          instrument_type, tick_size, lot_size, expiry, strike, is_active,
          created_at, updated_at
        FROM instruments
        WHERE exchange = $1 AND is_active = true
        ORDER BY symbol
        LIMIT $2
      `;

      const result = await this.pool.query(query, [exchange, limit]);
      return result.rows.map(row => this.mapRowToInstrument(row));
    } catch (error) {
      logger.error('Failed to get instruments by exchange:', error);
      throw error;
    }
  }

  /**
   * Update instrument status
   */
  async updateStatus(instrumentKey: string, isActive: boolean): Promise<boolean> {
    try {
      const query = `
        UPDATE instruments 
        SET is_active = $1, updated_at = CURRENT_TIMESTAMP
        WHERE instrument_key = $2
      `;

      const result = await this.pool.query(query, [isActive, instrumentKey]);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Failed to update instrument status:', error);
      throw error;
    }
  }

  /**
   * Get instrument statistics
   */
  async getStats(): Promise<{
    total: number;
    active: number;
    byExchange: Record<string, number>;
    bySegment: Record<string, number>;
  }> {
    try {
      const queries = [
        'SELECT COUNT(*) as total FROM instruments',
        'SELECT COUNT(*) as active FROM instruments WHERE is_active = true',
        'SELECT exchange, COUNT(*) as count FROM instruments WHERE is_active = true GROUP BY exchange',
        'SELECT segment, COUNT(*) as count FROM instruments WHERE is_active = true GROUP BY segment',
      ];

      const [totalResult, activeResult, exchangeResult, segmentResult] = await Promise.all(
        queries.map(query => this.pool.query(query))
      );

      const byExchange: Record<string, number> = {};
      exchangeResult.rows.forEach(row => {
        byExchange[row.exchange] = parseInt(row.count);
      });

      const bySegment: Record<string, number> = {};
      segmentResult.rows.forEach(row => {
        bySegment[row.segment] = parseInt(row.count);
      });

      return {
        total: parseInt(totalResult.rows[0].total),
        active: parseInt(activeResult.rows[0].active),
        byExchange,
        bySegment,
      };
    } catch (error) {
      logger.error('Failed to get instrument stats:', error);
      throw error;
    }
  }

  /**
   * Map database row to Instrument object
   */
  private mapRowToInstrument(row: any): Instrument {
    return {
      id: row.id,
      instrument_key: row.instrument_key,
      exchange_token: row.exchange_token,
      symbol: row.symbol,
      name: row.name,
      exchange: row.exchange,
      segment: row.segment,
      instrument_type: row.instrument_type,
      tick_size: parseFloat(row.tick_size),
      lot_size: row.lot_size,
      expiry: row.expiry ? new Date(row.expiry) : undefined,
      strike: row.strike ? parseFloat(row.strike) : undefined,
      is_active: row.is_active,
      created_at: new Date(row.created_at),
      updated_at: new Date(row.updated_at),
    };
  }
}
