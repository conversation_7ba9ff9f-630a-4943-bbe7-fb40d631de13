import { Pool } from 'pg';
import bcrypt from 'bcrypt';
import { User, UserRole } from '@trading-platform/shared';
import { config } from '../../config';

export class UserRepository {
  constructor(private pool: Pool) {}

  async create(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    role?: UserRole;
  }): Promise<User> {
    const passwordHash = await bcrypt.hash(userData.password, config.bcryptRounds);
    
    const query = `
      INSERT INTO users (email, password_hash, first_name, last_name, role)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id, email, first_name, last_name, role, is_active, created_at, updated_at
    `;
    
    const values = [
      userData.email,
      passwordHash,
      userData.firstName,
      userData.lastName,
      userData.role || 'trader',
    ];
    
    const result = await this.pool.query(query, values);
    return this.mapRowToUser(result.rows[0]);
  }

  async findById(id: string): Promise<User | null> {
    const query = `
      SELECT id, email, first_name, last_name, role, is_active, created_at, updated_at
      FROM users
      WHERE id = $1 AND is_active = true
    `;
    
    const result = await this.pool.query(query, [id]);
    return result.rows[0] ? this.mapRowToUser(result.rows[0]) : null;
  }

  async findByEmail(email: string): Promise<User | null> {
    const query = `
      SELECT id, email, first_name, last_name, role, is_active, created_at, updated_at
      FROM users
      WHERE email = $1 AND is_active = true
    `;
    
    const result = await this.pool.query(query, [email]);
    return result.rows[0] ? this.mapRowToUser(result.rows[0]) : null;
  }

  async findByEmailWithPassword(email: string): Promise<(User & { passwordHash: string }) | null> {
    const query = `
      SELECT id, email, password_hash, first_name, last_name, role, is_active, created_at, updated_at
      FROM users
      WHERE email = $1 AND is_active = true
    `;
    
    const result = await this.pool.query(query, [email]);
    if (!result.rows[0]) return null;
    
    const row = result.rows[0];
    return {
      ...this.mapRowToUser(row),
      passwordHash: row.password_hash,
    };
  }

  async update(id: string, updates: Partial<{
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    isActive: boolean;
  }>): Promise<User | null> {
    const fields = [];
    const values = [];
    let paramCount = 1;

    if (updates.email !== undefined) {
      fields.push(`email = $${paramCount++}`);
      values.push(updates.email);
    }
    if (updates.firstName !== undefined) {
      fields.push(`first_name = $${paramCount++}`);
      values.push(updates.firstName);
    }
    if (updates.lastName !== undefined) {
      fields.push(`last_name = $${paramCount++}`);
      values.push(updates.lastName);
    }
    if (updates.role !== undefined) {
      fields.push(`role = $${paramCount++}`);
      values.push(updates.role);
    }
    if (updates.isActive !== undefined) {
      fields.push(`is_active = $${paramCount++}`);
      values.push(updates.isActive);
    }

    if (fields.length === 0) {
      return this.findById(id);
    }

    const query = `
      UPDATE users
      SET ${fields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING id, email, first_name, last_name, role, is_active, created_at, updated_at
    `;
    
    values.push(id);
    const result = await this.pool.query(query, values);
    return result.rows[0] ? this.mapRowToUser(result.rows[0]) : null;
  }

  async updatePassword(id: string, newPassword: string): Promise<boolean> {
    const passwordHash = await bcrypt.hash(newPassword, config.bcryptRounds);
    
    const query = `
      UPDATE users
      SET password_hash = $1
      WHERE id = $2 AND is_active = true
    `;
    
    const result = await this.pool.query(query, [passwordHash, id]);
    return result.rowCount > 0;
  }

  async verifyPassword(email: string, password: string): Promise<User | null> {
    const userWithPassword = await this.findByEmailWithPassword(email);
    if (!userWithPassword) return null;

    const isValid = await bcrypt.compare(password, userWithPassword.passwordHash);
    if (!isValid) return null;

    const { passwordHash, ...user } = userWithPassword;
    return user;
  }

  async list(options: {
    page?: number;
    limit?: number;
    role?: UserRole;
    isActive?: boolean;
  } = {}): Promise<{ users: User[]; total: number }> {
    const { page = 1, limit = 20, role, isActive } = options;
    const offset = (page - 1) * limit;

    const conditions = [];
    const values = [];
    let paramCount = 1;

    if (role !== undefined) {
      conditions.push(`role = $${paramCount++}`);
      values.push(role);
    }
    if (isActive !== undefined) {
      conditions.push(`is_active = $${paramCount++}`);
      values.push(isActive);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM users ${whereClause}`;
    const countResult = await this.pool.query(countQuery, values);
    const total = parseInt(countResult.rows[0].count, 10);

    // Get users
    const query = `
      SELECT id, email, first_name, last_name, role, is_active, created_at, updated_at
      FROM users
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount++} OFFSET $${paramCount}
    `;
    
    values.push(limit, offset);
    const result = await this.pool.query(query, values);
    const users = result.rows.map(row => this.mapRowToUser(row));

    return { users, total };
  }

  async delete(id: string): Promise<boolean> {
    // Soft delete by setting is_active to false
    const query = `
      UPDATE users
      SET is_active = false
      WHERE id = $1
    `;
    
    const result = await this.pool.query(query, [id]);
    return result.rowCount > 0;
  }

  private mapRowToUser(row: any): User {
    return {
      id: row.id,
      email: row.email,
      firstName: row.first_name,
      lastName: row.last_name,
      role: row.role as UserRole,
      isActive: row.is_active,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }
}
