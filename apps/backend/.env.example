# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/trading_platform
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-influxdb-token
INFLUXDB_ORG=trading-org
INFLUXDB_BUCKET=market-data
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Server Configuration
PORT=3001
NODE_ENV=development
LOG_LEVEL=info

# External APIs
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-key
POLYGON_API_KEY=your-polygon-key
FINNHUB_API_KEY=your-finnhub-key

# WebSocket Configuration
WS_PORT=3002
WS_HEARTBEAT_INTERVAL=30000

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=60000

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
