{"name": "@trading-platform/frontend", "version": "1.0.0", "description": "Trading platform frontend application", "private": true, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "type-check": "tsc --noEmit", "clean": "rm -rf .next"}, "dependencies": {"@trading-platform/shared": "1.0.0", "next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@tanstack/react-query": "^5.0.0", "zustand": "^4.4.0", "axios": "^1.6.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.22.0", "recharts": "^2.8.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.292.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-separator": "^1.0.3", "react-hot-toast": "^2.4.0", "ws": "^8.14.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/ws": "^8.5.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.0", "jest": "^29.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.0.0"}}