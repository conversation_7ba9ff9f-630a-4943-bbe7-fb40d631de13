import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { Instrument, MarketTick, Watchlist } from '@trading-platform/shared';
import { apiClient } from '@/lib/api';

interface MarketDataState {
  // Market data
  ticks: Map<string, MarketTick>;
  instruments: Map<string, Instrument>;
  watchlists: Watchlist[];
  
  // UI state
  selectedInstruments: string[];
  searchResults: Instrument[];
  isSearching: boolean;
  
  // WebSocket state
  isConnected: boolean;
  subscribedInstruments: Set<string>;
  
  // Loading states
  isLoadingInstruments: boolean;
  isLoadingWatchlists: boolean;
  error: string | null;
}

interface MarketDataActions {
  // Market data actions
  updateTick: (tick: MarketTick) => void;
  setInstruments: (instruments: Instrument[]) => void;
  addInstrument: (instrument: Instrument) => void;
  
  // Search actions
  searchInstruments: (query: string) => Promise<void>;
  clearSearchResults: () => void;
  
  // Selection actions
  selectInstrument: (instrumentKey: string) => void;
  deselectInstrument: (instrumentKey: string) => void;
  clearSelection: () => void;
  
  // Watchlist actions
  loadWatchlists: () => Promise<void>;
  createWatchlist: (name: string, instrumentKeys: string[]) => Promise<void>;
  updateWatchlist: (id: string, updates: Partial<Watchlist>) => Promise<void>;
  deleteWatchlist: (id: string) => Promise<void>;
  addToWatchlist: (watchlistId: string, instrumentKey: string) => Promise<void>;
  removeFromWatchlist: (watchlistId: string, instrumentKey: string) => Promise<void>;
  
  // WebSocket actions
  setConnectionStatus: (connected: boolean) => void;
  addSubscription: (instrumentKey: string) => void;
  removeSubscription: (instrumentKey: string) => void;
  
  // Utility actions
  clearError: () => void;
  reset: () => void;
}

type MarketDataStore = MarketDataState & MarketDataActions;

const initialState: MarketDataState = {
  ticks: new Map(),
  instruments: new Map(),
  watchlists: [],
  selectedInstruments: [],
  searchResults: [],
  isSearching: false,
  isConnected: false,
  subscribedInstruments: new Set(),
  isLoadingInstruments: false,
  isLoadingWatchlists: false,
  error: null,
};

export const useMarketDataStore = create<MarketDataStore>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    // Market data actions
    updateTick: (tick: MarketTick) => {
      set((state) => {
        const newTicks = new Map(state.ticks);
        newTicks.set(tick.instrument_key, tick);
        return { ticks: newTicks };
      });
    },

    setInstruments: (instruments: Instrument[]) => {
      set((state) => {
        const newInstruments = new Map(state.instruments);
        instruments.forEach(instrument => {
          newInstruments.set(instrument.instrument_key, instrument);
        });
        return { instruments: newInstruments };
      });
    },

    addInstrument: (instrument: Instrument) => {
      set((state) => {
        const newInstruments = new Map(state.instruments);
        newInstruments.set(instrument.instrument_key, instrument);
        return { instruments: newInstruments };
      });
    },

    // Search actions
    searchInstruments: async (query: string) => {
      if (!query.trim()) {
        set({ searchResults: [] });
        return;
      }

      set({ isSearching: true, error: null });

      try {
        const response = await apiClient.get(`/api/instruments/search?q=${encodeURIComponent(query)}&limit=20`);
        const instruments = response.data.data;
        
        set({ 
          searchResults: instruments,
          isSearching: false,
        });

        // Add to instruments map
        get().setInstruments(instruments);
      } catch (error: any) {
        set({ 
          error: error.response?.data?.error || 'Failed to search instruments',
          isSearching: false,
          searchResults: [],
        });
      }
    },

    clearSearchResults: () => {
      set({ searchResults: [] });
    },

    // Selection actions
    selectInstrument: (instrumentKey: string) => {
      set((state) => ({
        selectedInstruments: [...state.selectedInstruments, instrumentKey],
      }));
    },

    deselectInstrument: (instrumentKey: string) => {
      set((state) => ({
        selectedInstruments: state.selectedInstruments.filter(key => key !== instrumentKey),
      }));
    },

    clearSelection: () => {
      set({ selectedInstruments: [] });
    },

    // Watchlist actions
    loadWatchlists: async () => {
      set({ isLoadingWatchlists: true, error: null });

      try {
        const response = await apiClient.get('/api/watchlists');
        const watchlists = response.data.data;
        
        set({ 
          watchlists,
          isLoadingWatchlists: false,
        });
      } catch (error: any) {
        set({ 
          error: error.response?.data?.error || 'Failed to load watchlists',
          isLoadingWatchlists: false,
        });
      }
    },

    createWatchlist: async (name: string, instrumentKeys: string[]) => {
      try {
        const response = await apiClient.post('/api/watchlists', {
          name,
          instruments: instrumentKeys,
        });
        
        const newWatchlist = response.data.data;
        set((state) => ({
          watchlists: [...state.watchlists, newWatchlist],
        }));
      } catch (error: any) {
        set({ error: error.response?.data?.error || 'Failed to create watchlist' });
        throw error;
      }
    },

    updateWatchlist: async (id: string, updates: Partial<Watchlist>) => {
      try {
        const response = await apiClient.patch(`/api/watchlists/${id}`, updates);
        const updatedWatchlist = response.data.data;
        
        set((state) => ({
          watchlists: state.watchlists.map(w => 
            w.id === id ? updatedWatchlist : w
          ),
        }));
      } catch (error: any) {
        set({ error: error.response?.data?.error || 'Failed to update watchlist' });
        throw error;
      }
    },

    deleteWatchlist: async (id: string) => {
      try {
        await apiClient.delete(`/api/watchlists/${id}`);
        
        set((state) => ({
          watchlists: state.watchlists.filter(w => w.id !== id),
        }));
      } catch (error: any) {
        set({ error: error.response?.data?.error || 'Failed to delete watchlist' });
        throw error;
      }
    },

    addToWatchlist: async (watchlistId: string, instrumentKey: string) => {
      try {
        const watchlist = get().watchlists.find(w => w.id === watchlistId);
        if (!watchlist) throw new Error('Watchlist not found');

        const updatedInstruments = [...watchlist.instruments, instrumentKey];
        await get().updateWatchlist(watchlistId, { instruments: updatedInstruments });
      } catch (error: any) {
        set({ error: error.response?.data?.error || 'Failed to add to watchlist' });
        throw error;
      }
    },

    removeFromWatchlist: async (watchlistId: string, instrumentKey: string) => {
      try {
        const watchlist = get().watchlists.find(w => w.id === watchlistId);
        if (!watchlist) throw new Error('Watchlist not found');

        const updatedInstruments = watchlist.instruments.filter(key => key !== instrumentKey);
        await get().updateWatchlist(watchlistId, { instruments: updatedInstruments });
      } catch (error: any) {
        set({ error: error.response?.data?.error || 'Failed to remove from watchlist' });
        throw error;
      }
    },

    // WebSocket actions
    setConnectionStatus: (connected: boolean) => {
      set({ isConnected: connected });
    },

    addSubscription: (instrumentKey: string) => {
      set((state) => {
        const newSubscriptions = new Set(state.subscribedInstruments);
        newSubscriptions.add(instrumentKey);
        return { subscribedInstruments: newSubscriptions };
      });
    },

    removeSubscription: (instrumentKey: string) => {
      set((state) => {
        const newSubscriptions = new Set(state.subscribedInstruments);
        newSubscriptions.delete(instrumentKey);
        return { subscribedInstruments: newSubscriptions };
      });
    },

    // Utility actions
    clearError: () => {
      set({ error: null });
    },

    reset: () => {
      set(initialState);
    },
  }))
);

// Selectors for computed values
export const useMarketDataSelectors = () => {
  const store = useMarketDataStore();
  
  return {
    // Get tick data for specific instrument
    getTick: (instrumentKey: string) => store.ticks.get(instrumentKey),
    
    // Get instrument data
    getInstrument: (instrumentKey: string) => store.instruments.get(instrumentKey),
    
    // Get selected instruments with their data
    getSelectedInstrumentsData: () => {
      return store.selectedInstruments.map(key => ({
        instrument: store.instruments.get(key),
        tick: store.ticks.get(key),
      })).filter(item => item.instrument);
    },
    
    // Get watchlist with instrument data
    getWatchlistWithData: (watchlistId: string) => {
      const watchlist = store.watchlists.find(w => w.id === watchlistId);
      if (!watchlist) return null;
      
      return {
        ...watchlist,
        instrumentsData: watchlist.instruments.map(key => ({
          instrument: store.instruments.get(key),
          tick: store.ticks.get(key),
        })).filter(item => item.instrument),
      };
    },
    
    // Check if instrument is subscribed
    isSubscribed: (instrumentKey: string) => store.subscribedInstruments.has(instrumentKey),
  };
};
