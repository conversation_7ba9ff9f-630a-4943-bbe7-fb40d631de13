'use client';

import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Activity, 
  Database, 
  Wifi, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Clock,
  TrendingUp
} from 'lucide-react';
import { apiClient } from '@/lib/api';
import { cn } from '@/lib/utils';

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: {
    database: {
      postgres: boolean;
      redis: boolean;
      influx: boolean;
    };
    upstox: {
      api: boolean;
      websocket: boolean;
    };
    marketData: {
      initialized: boolean;
      connected: boolean;
      subscribedCount: number;
    };
  };
  errors: string[];
}

interface SystemStatusProps {
  className?: string;
  showDetails?: boolean;
}

export function SystemStatus({ className, showDetails = true }: SystemStatusProps) {
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Fetch health status
  const { data: healthData, isLoading, error, refetch } = useQuery({
    queryKey: ['health-status'],
    queryFn: async () => {
      const response = await apiClient.get('/api/health');
      return response.data as HealthStatus;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
    retry: 3,
  });

  // Fetch market data status
  const { data: marketDataStatus } = useQuery({
    queryKey: ['market-data-status'],
    queryFn: async () => {
      const response = await apiClient.marketData.getStatus();
      return response.data.data;
    },
    refetchInterval: 10000, // Refetch every 10 seconds
    retry: 1,
  });

  useEffect(() => {
    if (healthData) {
      setLastUpdate(new Date());
    }
  }, [healthData]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-profit" />;
      case 'degraded':
        return <AlertTriangle className="h-5 w-5 text-warning" />;
      case 'unhealthy':
        return <XCircle className="h-5 w-5 text-loss" />;
      default:
        return <Activity className="h-5 w-5 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-profit';
      case 'degraded':
        return 'text-warning';
      case 'unhealthy':
        return 'text-loss';
      default:
        return 'text-muted-foreground';
    }
  };

  const getServiceStatus = (isHealthy: boolean) => {
    return isHealthy ? (
      <CheckCircle className="h-4 w-4 text-profit" />
    ) : (
      <XCircle className="h-4 w-4 text-loss" />
    );
  };

  if (isLoading) {
    return (
      <div className={cn("bg-card border border-border rounded-lg p-6", className)}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-muted rounded w-1/3"></div>
          <div className="space-y-2">
            <div className="h-4 bg-muted rounded"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("bg-card border border-border rounded-lg p-6", className)}>
        <div className="flex items-center space-x-2 text-destructive">
          <XCircle className="h-5 w-5" />
          <span className="font-medium">Unable to fetch system status</span>
        </div>
        <button
          onClick={() => refetch()}
          className="mt-2 text-sm text-muted-foreground hover:text-foreground"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className={cn("bg-card border border-border rounded-lg p-6 space-y-6", className)}>
      {/* Overall Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {getStatusIcon(healthData?.status || 'unknown')}
          <div>
            <h3 className="font-semibold">System Status</h3>
            <p className={cn("text-sm capitalize", getStatusColor(healthData?.status || 'unknown'))}>
              {healthData?.status || 'Unknown'}
            </p>
          </div>
        </div>
        
        {lastUpdate && (
          <div className="text-right">
            <div className="text-xs text-muted-foreground">Last updated</div>
            <div className="text-sm">{lastUpdate.toLocaleTimeString()}</div>
          </div>
        )}
      </div>

      {/* Error Messages */}
      {healthData?.errors && healthData.errors.length > 0 && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <AlertTriangle className="h-4 w-4 text-destructive" />
            <span className="font-medium text-destructive">Issues Detected</span>
          </div>
          <ul className="text-sm text-destructive space-y-1">
            {healthData.errors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Service Details */}
      {showDetails && healthData && (
        <div className="space-y-4">
          {/* Database Services */}
          <div>
            <h4 className="font-medium mb-2 flex items-center space-x-2">
              <Database className="h-4 w-4" />
              <span>Database Services</span>
            </h4>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="flex items-center justify-between">
                <span>PostgreSQL</span>
                {getServiceStatus(healthData.services.database.postgres)}
              </div>
              <div className="flex items-center justify-between">
                <span>Redis</span>
                {getServiceStatus(healthData.services.database.redis)}
              </div>
              <div className="flex items-center justify-between">
                <span>InfluxDB</span>
                {getServiceStatus(healthData.services.database.influx)}
              </div>
            </div>
          </div>

          {/* Upstox Services */}
          <div>
            <h4 className="font-medium mb-2 flex items-center space-x-2">
              <TrendingUp className="h-4 w-4" />
              <span>Upstox Services</span>
            </h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center justify-between">
                <span>API</span>
                {getServiceStatus(healthData.services.upstox.api)}
              </div>
              <div className="flex items-center justify-between">
                <span>WebSocket</span>
                {getServiceStatus(healthData.services.upstox.websocket)}
              </div>
            </div>
          </div>

          {/* Market Data Service */}
          <div>
            <h4 className="font-medium mb-2 flex items-center space-x-2">
              <Wifi className="h-4 w-4" />
              <span>Market Data</span>
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-center justify-between">
                <span>Service Initialized</span>
                {getServiceStatus(healthData.services.marketData.initialized)}
              </div>
              <div className="flex items-center justify-between">
                <span>WebSocket Connected</span>
                {getServiceStatus(healthData.services.marketData.connected)}
              </div>
              <div className="flex items-center justify-between">
                <span>Active Subscriptions</span>
                <span className="font-medium">
                  {healthData.services.marketData.subscribedCount}
                </span>
              </div>
            </div>
          </div>

          {/* Market Data Status Details */}
          {marketDataStatus && (
            <div>
              <h4 className="font-medium mb-2 flex items-center space-x-2">
                <Activity className="h-4 w-4" />
                <span>Market Data Details</span>
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center justify-between">
                  <span>Cache Size</span>
                  <span className="font-medium">{marketDataStatus.cacheSize}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>WS Reconnect Attempts</span>
                  <span className="font-medium">
                    {marketDataStatus.wsStatus?.reconnectAttempts || 0}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Refresh Button */}
      <div className="flex justify-center pt-4 border-t border-border">
        <button
          onClick={() => refetch()}
          className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
        >
          <Clock className="h-4 w-4" />
          <span>Refresh Status</span>
        </button>
      </div>
    </div>
  );
}
