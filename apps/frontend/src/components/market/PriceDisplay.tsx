'use client';

import { useEffect, useState } from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { MarketTick, Instrument } from '@trading-platform/shared';
import { cn, formatCurrency, formatPercentage, getColorForPnL } from '@/lib/utils';

interface PriceDisplayProps {
  instrument: Instrument;
  tick?: MarketTick;
  size?: 'sm' | 'md' | 'lg';
  showChange?: boolean;
  showPercentage?: boolean;
  showVolume?: boolean;
  className?: string;
}

export function PriceDisplay({
  instrument,
  tick,
  size = 'md',
  showChange = true,
  showPercentage = true,
  showVolume = false,
  className,
}: PriceDisplayProps) {
  const [priceAnimation, setPriceAnimation] = useState<'up' | 'down' | null>(null);
  const [previousPrice, setPreviousPrice] = useState<number | null>(null);

  const currentPrice = tick?.last_price || 0;
  const priceChange = tick?.net_change || 0;
  const percentageChange = previousPrice && previousPrice !== 0 
    ? ((currentPrice - previousPrice) / previousPrice) * 100 
    : 0;

  const isPositive = priceChange > 0;
  const isNegative = priceChange < 0;
  const isNeutral = priceChange === 0;

  // Animate price changes
  useEffect(() => {
    if (previousPrice !== null && currentPrice !== previousPrice) {
      setPriceAnimation(currentPrice > previousPrice ? 'up' : 'down');
      
      const timer = setTimeout(() => {
        setPriceAnimation(null);
      }, 1000);

      return () => clearTimeout(timer);
    }
    setPreviousPrice(currentPrice);
  }, [currentPrice, previousPrice]);

  const sizeClasses = {
    sm: {
      price: 'text-lg font-semibold',
      change: 'text-sm',
      symbol: 'text-sm font-medium',
      volume: 'text-xs',
    },
    md: {
      price: 'text-2xl font-bold',
      change: 'text-base',
      symbol: 'text-lg font-semibold',
      volume: 'text-sm',
    },
    lg: {
      price: 'text-4xl font-bold',
      change: 'text-xl',
      symbol: 'text-2xl font-bold',
      volume: 'text-base',
    },
  };

  const classes = sizeClasses[size];

  return (
    <div className={cn("space-y-2", className)}>
      {/* Symbol and Exchange */}
      <div className="flex items-center space-x-2">
        <h3 className={classes.symbol}>{instrument.symbol}</h3>
        <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
          {instrument.exchange}
        </span>
      </div>

      {/* Company Name */}
      <p className="text-sm text-muted-foreground truncate">
        {instrument.name}
      </p>

      {/* Price */}
      <div className="flex items-center space-x-3">
        <span 
          className={cn(
            classes.price,
            priceAnimation === 'up' && 'animate-pulse-green',
            priceAnimation === 'down' && 'animate-pulse-red'
          )}
        >
          {currentPrice > 0 ? formatCurrency(currentPrice) : '--'}
        </span>

        {tick && (
          <div className="flex items-center space-x-1">
            <div className={cn(
              "w-2 h-2 rounded-full",
              isPositive && "bg-profit animate-pulse",
              isNegative && "bg-loss animate-pulse",
              isNeutral && "bg-muted-foreground"
            )} />
            <span className="text-xs text-muted-foreground">
              Live
            </span>
          </div>
        )}
      </div>

      {/* Price Change */}
      {showChange && tick && (
        <div className="flex items-center space-x-4">
          <div className={cn(
            "flex items-center space-x-1",
            classes.change,
            getColorForPnL(priceChange)
          )}>
            {isPositive && <TrendingUp className="h-4 w-4" />}
            {isNegative && <TrendingDown className="h-4 w-4" />}
            {isNeutral && <Minus className="h-4 w-4" />}
            <span>
              {priceChange >= 0 ? '+' : ''}{formatCurrency(Math.abs(priceChange))}
            </span>
          </div>

          {showPercentage && (
            <div className={cn(
              classes.change,
              getColorForPnL(priceChange)
            )}>
              ({priceChange >= 0 ? '+' : ''}{formatPercentage(percentageChange / 100)})
            </div>
          )}
        </div>
      )}

      {/* Volume */}
      {showVolume && tick && tick.volume > 0 && (
        <div className={cn("text-muted-foreground", classes.volume)}>
          Volume: {tick.volume.toLocaleString()}
        </div>
      )}

      {/* Additional Market Data */}
      {tick && size === 'lg' && (
        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-border">
          <div>
            <div className="text-xs text-muted-foreground">Buy Quantity</div>
            <div className="text-sm font-medium">
              {tick.total_buy_quantity.toLocaleString()}
            </div>
          </div>
          <div>
            <div className="text-xs text-muted-foreground">Sell Quantity</div>
            <div className="text-sm font-medium">
              {tick.total_sell_quantity.toLocaleString()}
            </div>
          </div>
          {tick.oi !== undefined && tick.oi > 0 && (
            <>
              <div>
                <div className="text-xs text-muted-foreground">Open Interest</div>
                <div className="text-sm font-medium">
                  {tick.oi.toLocaleString()}
                </div>
              </div>
            </>
          )}
        </div>
      )}

      {/* Last Update Time */}
      {tick && (
        <div className="text-xs text-muted-foreground">
          Last updated: {new Date(tick.last_trade_time).toLocaleTimeString()}
        </div>
      )}
    </div>
  );
}
