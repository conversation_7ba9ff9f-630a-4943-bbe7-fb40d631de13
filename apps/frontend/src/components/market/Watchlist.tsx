'use client';

import { useState, useEffect } from 'react';
import { Plus, MoreVertical, TrendingUp, TrendingDown, Star, Trash2 } from 'lucide-react';
import { useMarketDataStore, useMarketDataSelectors } from '@/stores/marketDataStore';
import { useWebSocket } from '@/hooks/useWebSocket';
import { Watchlist as WatchlistType } from '@trading-platform/shared';
import { cn, formatCurrency, getColorForPnL } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface WatchlistProps {
  watchlistId?: string;
  title?: string;
  showHeader?: boolean;
  className?: string;
}

export function Watchlist({
  watchlistId,
  title = "Watchlist",
  showHeader = true,
  className,
}: WatchlistProps) {
  const [selectedWatchlist, setSelectedWatchlist] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newWatchlistName, setNewWatchlistName] = useState('');

  const {
    watchlists,
    isLoadingWatchlists,
    loadWatchlists,
    createWatchlist,
    deleteWatchlist,
    removeFromWatchlist,
    error,
  } = useMarketDataStore();

  const { getWatchlistWithData, getTick, getInstrument } = useMarketDataSelectors();
  const { subscribe, unsubscribe } = useWebSocket();

  // Load watchlists on mount
  useEffect(() => {
    loadWatchlists();
  }, [loadWatchlists]);

  // Set default watchlist
  useEffect(() => {
    if (watchlistId) {
      setSelectedWatchlist(watchlistId);
    } else if (watchlists.length > 0 && !selectedWatchlist) {
      const defaultWatchlist = watchlists.find(w => w.is_default) || watchlists[0];
      setSelectedWatchlist(defaultWatchlist.id);
    }
  }, [watchlists, watchlistId, selectedWatchlist]);

  // Subscribe to instruments in selected watchlist
  useEffect(() => {
    if (selectedWatchlist) {
      const watchlist = watchlists.find(w => w.id === selectedWatchlist);
      if (watchlist && watchlist.instruments.length > 0) {
        subscribe(watchlist.instruments);
        
        return () => {
          unsubscribe(watchlist.instruments);
        };
      }
    }
  }, [selectedWatchlist, watchlists, subscribe, unsubscribe]);

  const currentWatchlist = selectedWatchlist 
    ? getWatchlistWithData(selectedWatchlist)
    : null;

  const handleCreateWatchlist = async () => {
    if (!newWatchlistName.trim()) return;

    try {
      await createWatchlist(newWatchlistName, []);
      setNewWatchlistName('');
      setShowCreateForm(false);
    } catch (error) {
      console.error('Failed to create watchlist:', error);
    }
  };

  const handleDeleteWatchlist = async (id: string) => {
    if (confirm('Are you sure you want to delete this watchlist?')) {
      try {
        await deleteWatchlist(id);
        if (selectedWatchlist === id) {
          setSelectedWatchlist(null);
        }
      } catch (error) {
        console.error('Failed to delete watchlist:', error);
      }
    }
  };

  const handleRemoveInstrument = async (instrumentKey: string) => {
    if (!selectedWatchlist) return;

    try {
      await removeFromWatchlist(selectedWatchlist, instrumentKey);
    } catch (error) {
      console.error('Failed to remove instrument:', error);
    }
  };

  if (isLoadingWatchlists) {
    return (
      <div className={cn("space-y-4", className)}>
        {showHeader && (
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">{title}</h2>
          </div>
        )}
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-muted animate-pulse rounded" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {showHeader && (
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">{title}</h2>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowCreateForm(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            New List
          </Button>
        </div>
      )}

      {/* Watchlist Tabs */}
      {watchlists.length > 1 && (
        <div className="flex space-x-1 border-b border-border">
          {watchlists.map((watchlist) => (
            <button
              key={watchlist.id}
              onClick={() => setSelectedWatchlist(watchlist.id)}
              className={cn(
                "px-3 py-2 text-sm font-medium border-b-2 transition-colors",
                selectedWatchlist === watchlist.id
                  ? "border-primary text-primary"
                  : "border-transparent text-muted-foreground hover:text-foreground"
              )}
            >
              {watchlist.name}
              {watchlist.is_default && (
                <Star className="inline h-3 w-3 ml-1 fill-current" />
              )}
            </button>
          ))}
        </div>
      )}

      {/* Create Watchlist Form */}
      {showCreateForm && (
        <div className="p-4 border border-border rounded-lg bg-card">
          <div className="space-y-3">
            <input
              type="text"
              value={newWatchlistName}
              onChange={(e) => setNewWatchlistName(e.target.value)}
              placeholder="Watchlist name"
              className="w-full px-3 py-2 border border-input rounded-md"
              onKeyPress={(e) => e.key === 'Enter' && handleCreateWatchlist()}
            />
            <div className="flex space-x-2">
              <Button size="sm" onClick={handleCreateWatchlist}>
                Create
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setShowCreateForm(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Watchlist Content */}
      {currentWatchlist ? (
        <div className="space-y-2">
          {currentWatchlist.instrumentsData.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>No instruments in this watchlist</p>
              <p className="text-sm">Use the search to add instruments</p>
            </div>
          ) : (
            currentWatchlist.instrumentsData.map(({ instrument, tick }) => {
              if (!instrument) return null;

              const priceChange = tick?.net_change || 0;
              const isPositive = priceChange > 0;
              const isNegative = priceChange < 0;

              return (
                <div
                  key={instrument.instrument_key}
                  className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-accent transition-colors group"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{instrument.symbol}</span>
                      <span className="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded">
                        {instrument.exchange}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground truncate">
                      {instrument.name}
                    </p>
                  </div>

                  <div className="text-right">
                    <div className="font-medium">
                      {tick?.last_price ? formatCurrency(tick.last_price) : '--'}
                    </div>
                    {tick && (
                      <div className={cn(
                        "text-sm flex items-center justify-end space-x-1",
                        getColorForPnL(priceChange)
                      )}>
                        {isPositive && <TrendingUp className="h-3 w-3" />}
                        {isNegative && <TrendingDown className="h-3 w-3" />}
                        <span>
                          {priceChange >= 0 ? '+' : ''}{formatCurrency(priceChange)}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={() => handleRemoveInstrument(instrument.instrument_key)}
                      className="p-1 hover:bg-destructive/10 hover:text-destructive rounded"
                      title="Remove from watchlist"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              );
            })
          )}
        </div>
      ) : (
        <div className="text-center py-8 text-muted-foreground">
          <p>No watchlist selected</p>
          {watchlists.length === 0 && (
            <p className="text-sm">Create your first watchlist to get started</p>
          )}
        </div>
      )}

      {error && (
        <div className="p-3 bg-destructive/10 text-destructive text-sm rounded-lg">
          {error}
        </div>
      )}
    </div>
  );
}
