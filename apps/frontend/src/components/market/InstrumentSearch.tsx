'use client';

import { useState, useEffect, useRef } from 'react';
import { Search, Plus, TrendingUp, TrendingDown } from 'lucide-react';
import { useMarketDataStore, useMarketDataSelectors } from '@/stores/marketDataStore';
import { Instrument } from '@trading-platform/shared';
import { cn, formatCurrency, getColorForPnL } from '@/lib/utils';

interface InstrumentSearchProps {
  onSelect?: (instrument: Instrument) => void;
  placeholder?: string;
  showAddButton?: boolean;
  className?: string;
}

export function InstrumentSearch({
  onSelect,
  placeholder = "Search stocks, ETFs, indices...",
  showAddButton = true,
  className,
}: InstrumentSearchProps) {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const {
    searchResults,
    isSearching,
    searchInstruments,
    clearSearchResults,
    selectInstrument,
  } = useMarketDataStore();

  const { getTick } = useMarketDataSelectors();

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (query.trim()) {
        searchInstruments(query);
        setIsOpen(true);
      } else {
        clearSearchResults();
        setIsOpen(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query, searchInstruments, clearSearchResults]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelect = (instrument: Instrument) => {
    setQuery('');
    setIsOpen(false);
    clearSearchResults();
    
    if (onSelect) {
      onSelect(instrument);
    } else {
      selectInstrument(instrument.instrument_key);
    }
  };

  const handleAddToWatchlist = (instrument: Instrument, event: React.MouseEvent) => {
    event.stopPropagation();
    // TODO: Implement add to watchlist functionality
    console.log('Add to watchlist:', instrument.symbol);
  };

  return (
    <div ref={searchRef} className={cn("relative", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => query && setIsOpen(true)}
          placeholder={placeholder}
          className="w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
        />
        {isSearching && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          </div>
        )}
      </div>

      {isOpen && (searchResults.length > 0 || isSearching) && (
        <div className="absolute z-50 w-full mt-1 bg-popover border border-border rounded-md shadow-lg max-h-96 overflow-y-auto">
          {isSearching ? (
            <div className="p-4 text-center text-muted-foreground">
              Searching...
            </div>
          ) : searchResults.length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">
              No instruments found
            </div>
          ) : (
            <div className="py-2">
              {searchResults.map((instrument) => {
                const tick = getTick(instrument.instrument_key);
                const hasPrice = tick && tick.last_price > 0;
                const priceChange = tick ? tick.net_change : 0;
                const isPositive = priceChange > 0;
                const isNegative = priceChange < 0;

                return (
                  <div
                    key={instrument.instrument_key}
                    onClick={() => handleSelect(instrument)}
                    className="flex items-center justify-between px-4 py-3 hover:bg-accent cursor-pointer group"
                  >
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-foreground">
                          {instrument.symbol}
                        </span>
                        <span className="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded">
                          {instrument.exchange}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground truncate">
                        {instrument.name}
                      </p>
                    </div>

                    <div className="flex items-center space-x-3">
                      {hasPrice && (
                        <div className="text-right">
                          <div className="font-medium">
                            {formatCurrency(tick.last_price)}
                          </div>
                          <div className={cn(
                            "text-xs flex items-center space-x-1",
                            getColorForPnL(priceChange)
                          )}>
                            {isPositive && <TrendingUp className="h-3 w-3" />}
                            {isNegative && <TrendingDown className="h-3 w-3" />}
                            <span>
                              {priceChange >= 0 ? '+' : ''}{formatCurrency(priceChange)}
                            </span>
                          </div>
                        </div>
                      )}

                      {showAddButton && (
                        <button
                          onClick={(e) => handleAddToWatchlist(instrument, e)}
                          className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-muted rounded"
                          title="Add to watchlist"
                        >
                          <Plus className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
