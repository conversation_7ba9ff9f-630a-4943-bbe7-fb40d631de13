import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse } from '@trading-platform/shared';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API methods
export const apiClient = {
  // Generic methods
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    api.get(url, config),
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    api.post(url, data, config),
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    api.put(url, data, config),
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    api.patch(url, data, config),
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    api.delete(url, config),

  // Auth methods
  auth: {
    login: (email: string, password: string) =>
      api.post('/api/auth/login', { email, password }),
    
    register: (email: string, password: string, firstName: string, lastName: string) =>
      api.post('/api/auth/register', { email, password, firstName, lastName }),
    
    refreshToken: (token: string) =>
      api.post('/api/auth/refresh', { token }),
    
    getCurrentUser: () =>
      api.get('/api/auth/me'),
    
    changePassword: (currentPassword: string, newPassword: string) =>
      api.post('/api/auth/change-password', { currentPassword, newPassword }),
    
    resetPassword: (email: string) =>
      api.post('/api/auth/reset-password', { email }),
    
    confirmPasswordReset: (token: string, newPassword: string) =>
      api.post('/api/auth/confirm-reset', { token, newPassword }),
    
    logout: () =>
      api.post('/api/auth/logout'),
  },

  // Trading methods
  trading: {
    getOrders: (params?: { page?: number; limit?: number; status?: string }) =>
      api.get('/api/orders', { params }),
    
    createOrder: (order: {
      symbol: string;
      type: string;
      side: string;
      quantity: number;
      price?: number;
      stopPrice?: number;
    }) =>
      api.post('/api/orders', order),
    
    cancelOrder: (orderId: string) =>
      api.delete(`/api/orders/${orderId}`),
    
    getPositions: () =>
      api.get('/api/positions'),
    
    getPortfolio: () =>
      api.get('/api/portfolio'),
  },

  // Strategy methods
  strategies: {
    getStrategies: (params?: { page?: number; limit?: number; status?: string }) =>
      api.get('/api/strategies', { params }),
    
    createStrategy: (strategy: {
      name: string;
      description?: string;
      type: string;
      parameters: Record<string, any>;
    }) =>
      api.post('/api/strategies', strategy),
    
    updateStrategy: (id: string, updates: Partial<{
      name: string;
      description: string;
      status: string;
      parameters: Record<string, any>;
    }>) =>
      api.patch(`/api/strategies/${id}`, updates),
    
    deleteStrategy: (id: string) =>
      api.delete(`/api/strategies/${id}`),
    
    backtest: (strategyId: string, params: {
      startDate: string;
      endDate: string;
      initialCapital: number;
    }) =>
      api.post(`/api/strategies/${strategyId}/backtest`, params),
    
    getBacktests: (strategyId: string) =>
      api.get(`/api/strategies/${strategyId}/backtests`),
  },

  // Market data methods
  market: {
    getQuote: (symbol: string) =>
      api.get(`/api/market/quote/${symbol}`),
    
    getQuotes: (symbols: string[]) =>
      api.post('/api/market/quotes', { symbols }),
    
    getHistoricalData: (symbol: string, params: {
      interval: string;
      startDate: string;
      endDate: string;
    }) =>
      api.get(`/api/market/historical/${symbol}`, { params }),
    
    searchSymbols: (query: string) =>
      api.get('/api/market/search', { params: { q: query } }),
  },

  // Analytics methods
  analytics: {
    getPerformanceMetrics: (params?: {
      startDate?: string;
      endDate?: string;
      strategyId?: string;
    }) =>
      api.get('/api/analytics/performance', { params }),
    
    getRiskMetrics: () =>
      api.get('/api/analytics/risk'),
    
    getTradingStats: (params?: {
      period?: string;
      strategyId?: string;
    }) =>
      api.get('/api/analytics/stats', { params }),
  },

  // Notifications methods
  notifications: {
    getNotifications: (params?: { page?: number; limit?: number; unread?: boolean }) =>
      api.get('/api/notifications', { params }),
    
    markAsRead: (notificationId: string) =>
      api.patch(`/api/notifications/${notificationId}/read`),
    
    markAllAsRead: () =>
      api.patch('/api/notifications/read-all'),
    
    deleteNotification: (notificationId: string) =>
      api.delete(`/api/notifications/${notificationId}`),
  },

  // Health check
  health: () =>
    api.get('/api/health'),
};

export default apiClient;
