import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse } from '@trading-platform/shared';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API methods
export const apiClient = {
  // Generic methods
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    api.get(url, config),
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    api.post(url, data, config),
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    api.put(url, data, config),
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    api.patch(url, data, config),
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    api.delete(url, config),

  // Auth methods
  auth: {
    login: (email: string, password: string) =>
      api.post('/api/auth/login', { email, password }),
    
    register: (email: string, password: string, firstName: string, lastName: string) =>
      api.post('/api/auth/register', { email, password, firstName, lastName }),
    
    refreshToken: (token: string) =>
      api.post('/api/auth/refresh', { token }),
    
    getCurrentUser: () =>
      api.get('/api/auth/me'),
    
    changePassword: (currentPassword: string, newPassword: string) =>
      api.post('/api/auth/change-password', { currentPassword, newPassword }),
    
    resetPassword: (email: string) =>
      api.post('/api/auth/reset-password', { email }),
    
    confirmPasswordReset: (token: string, newPassword: string) =>
      api.post('/api/auth/confirm-reset', { token, newPassword }),
    
    logout: () =>
      api.post('/api/auth/logout'),
  },

  // Trading methods
  trading: {
    getOrders: (params?: { page?: number; limit?: number; status?: string }) =>
      api.get('/api/orders', { params }),
    
    createOrder: (order: {
      symbol: string;
      type: string;
      side: string;
      quantity: number;
      price?: number;
      stopPrice?: number;
    }) =>
      api.post('/api/orders', order),
    
    cancelOrder: (orderId: string) =>
      api.delete(`/api/orders/${orderId}`),
    
    getPositions: () =>
      api.get('/api/positions'),
    
    getPortfolio: () =>
      api.get('/api/portfolio'),
  },

  // Strategy methods
  strategies: {
    getStrategies: (params?: { page?: number; limit?: number; status?: string }) =>
      api.get('/api/strategies', { params }),
    
    createStrategy: (strategy: {
      name: string;
      description?: string;
      type: string;
      parameters: Record<string, any>;
    }) =>
      api.post('/api/strategies', strategy),
    
    updateStrategy: (id: string, updates: Partial<{
      name: string;
      description: string;
      status: string;
      parameters: Record<string, any>;
    }>) =>
      api.patch(`/api/strategies/${id}`, updates),
    
    deleteStrategy: (id: string) =>
      api.delete(`/api/strategies/${id}`),
    
    backtest: (strategyId: string, params: {
      startDate: string;
      endDate: string;
      initialCapital: number;
    }) =>
      api.post(`/api/strategies/${strategyId}/backtest`, params),
    
    getBacktests: (strategyId: string) =>
      api.get(`/api/strategies/${strategyId}/backtests`),
  },

  // Instruments methods
  instruments: {
    search: (query: string, params?: {
      limit?: number;
      exchanges?: string;
      segments?: string;
    }) =>
      api.get('/api/instruments/search', {
        params: { q: query, ...params }
      }),

    getPopular: (limit?: number) =>
      api.get('/api/instruments/popular', {
        params: { limit }
      }),

    getByKey: (instrumentKey: string) =>
      api.get(`/api/instruments/${instrumentKey}`),

    getByExchange: (exchange: string, limit?: number) =>
      api.get(`/api/instruments/exchange/${exchange}`, {
        params: { limit }
      }),

    getStats: () =>
      api.get('/api/instruments/stats'),

    sync: () =>
      api.post('/api/instruments/sync'),
  },

  // Market data methods
  marketData: {
    getCurrent: (instrumentKeys: string[]) =>
      api.post('/api/market-data/current', { instrument_keys: instrumentKeys }),

    subscribe: (subscriptions: Array<{
      instrument_key: string;
      mode: 'ltpc' | 'full';
    }>) =>
      api.post('/api/market-data/subscribe', { subscriptions }),

    unsubscribe: (instrumentKeys: string[]) =>
      api.post('/api/market-data/unsubscribe', { instrument_keys: instrumentKeys }),

    getStatus: () =>
      api.get('/api/market-data/status'),

    getHistorical: (params: {
      instrument_key: string;
      start_time: string;
      end_time?: string;
      interval?: string;
    }) =>
      api.get('/api/market-data/historical', { params }),

    getQuotes: (instrumentKeys: string[]) =>
      api.post('/api/market-data/quotes', { instrument_keys: instrumentKeys }),
  },

  // Watchlists methods
  watchlists: {
    getAll: () =>
      api.get('/api/watchlists'),

    create: (data: {
      name: string;
      instruments: string[];
      is_default?: boolean;
    }) =>
      api.post('/api/watchlists', data),

    update: (id: string, data: Partial<{
      name: string;
      instruments: string[];
      is_default: boolean;
    }>) =>
      api.patch(`/api/watchlists/${id}`, data),

    delete: (id: string) =>
      api.delete(`/api/watchlists/${id}`),

    addInstrument: (id: string, instrumentKey: string) =>
      api.post(`/api/watchlists/${id}/instruments`, { instrument_key: instrumentKey }),

    removeInstrument: (id: string, instrumentKey: string) =>
      api.delete(`/api/watchlists/${id}/instruments/${instrumentKey}`),
  },

  // Analytics methods
  analytics: {
    getPerformanceMetrics: (params?: {
      startDate?: string;
      endDate?: string;
      strategyId?: string;
    }) =>
      api.get('/api/analytics/performance', { params }),
    
    getRiskMetrics: () =>
      api.get('/api/analytics/risk'),
    
    getTradingStats: (params?: {
      period?: string;
      strategyId?: string;
    }) =>
      api.get('/api/analytics/stats', { params }),
  },

  // Notifications methods
  notifications: {
    getNotifications: (params?: { page?: number; limit?: number; unread?: boolean }) =>
      api.get('/api/notifications', { params }),
    
    markAsRead: (notificationId: string) =>
      api.patch(`/api/notifications/${notificationId}/read`),
    
    markAllAsRead: () =>
      api.patch('/api/notifications/read-all'),
    
    deleteNotification: (notificationId: string) =>
      api.delete(`/api/notifications/${notificationId}`),
  },

  // Health check
  health: () =>
    api.get('/api/health'),
};

export default apiClient;
