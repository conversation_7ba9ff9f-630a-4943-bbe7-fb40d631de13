import { useEffect, useRef, useState, useCallback } from 'react';
import { WSMarketDataMessage, MarketTick } from '@trading-platform/shared';
import { useAuthStore } from '@/stores/authStore';

export interface WebSocketStatus {
  connected: boolean;
  connecting: boolean;
  error: string | null;
  lastMessage: WSMarketDataMessage | null;
}

export interface UseWebSocketReturn {
  status: WebSocketStatus;
  subscribe: (instrumentKeys: string[]) => void;
  unsubscribe: (instrumentKeys: string[]) => void;
  sendMessage: (message: any) => void;
  marketData: Map<string, MarketTick>;
}

export const useWebSocket = (url?: string): UseWebSocketReturn => {
  const { token } = useAuthStore();
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  
  const [status, setStatus] = useState<WebSocketStatus>({
    connected: false,
    connecting: false,
    error: null,
    lastMessage: null,
  });
  
  const [marketData, setMarketData] = useState<Map<string, MarketTick>>(new Map());
  
  const wsUrl = url || `${process.env.NEXT_PUBLIC_WS_URL?.replace('http', 'ws')}/ws/market-data`;

  const connect = useCallback(() => {
    if (!token) {
      setStatus(prev => ({ ...prev, error: 'No authentication token' }));
      return;
    }

    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setStatus(prev => ({ ...prev, connecting: true, error: null }));

    try {
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('WebSocket connected');
        setStatus(prev => ({
          ...prev,
          connected: true,
          connecting: false,
          error: null,
        }));

        // Start heartbeat
        heartbeatIntervalRef.current = setInterval(() => {
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({ type: 'ping' }));
          }
        }, 30000);
      };

      ws.onmessage = (event) => {
        try {
          const message: WSMarketDataMessage = JSON.parse(event.data);
          
          setStatus(prev => ({ ...prev, lastMessage: message }));

          switch (message.type) {
            case 'tick':
              const tick = message.data as MarketTick;
              setMarketData(prev => {
                const newMap = new Map(prev);
                newMap.set(tick.instrument_key, tick);
                return newMap;
              });
              break;

            case 'connection_status':
              console.log('Connection status:', message.data);
              break;

            case 'subscription_success':
              console.log('Subscription success:', message.data);
              break;

            case 'subscription_error':
              console.error('Subscription error:', message.data);
              setStatus(prev => ({ ...prev, error: message.data.message }));
              break;

            default:
              console.log('Unknown message type:', message.type);
          }
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      ws.onclose = (event) => {
        console.log('WebSocket closed:', event.code, event.reason);
        setStatus(prev => ({
          ...prev,
          connected: false,
          connecting: false,
          error: event.reason || 'Connection closed',
        }));

        // Clear heartbeat
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
          heartbeatIntervalRef.current = null;
        }

        // Attempt to reconnect after 5 seconds
        if (event.code !== 1000) { // Not a normal closure
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, 5000);
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setStatus(prev => ({
          ...prev,
          connected: false,
          connecting: false,
          error: 'WebSocket error',
        }));
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setStatus(prev => ({
        ...prev,
        connected: false,
        connecting: false,
        error: 'Failed to connect',
      }));
    }
  }, [wsUrl, token]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    setStatus({
      connected: false,
      connecting: false,
      error: null,
      lastMessage: null,
    });
  }, []);

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }, []);

  const subscribe = useCallback((instrumentKeys: string[]) => {
    sendMessage({
      type: 'subscribe',
      instrument_keys: instrumentKeys,
    });
  }, [sendMessage]);

  const unsubscribe = useCallback((instrumentKeys: string[]) => {
    sendMessage({
      type: 'unsubscribe',
      instrument_keys: instrumentKeys,
    });
  }, [sendMessage]);

  // Connect on mount and when token changes
  useEffect(() => {
    if (token) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [token, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    status,
    subscribe,
    unsubscribe,
    sendMessage,
    marketData,
  };
};
