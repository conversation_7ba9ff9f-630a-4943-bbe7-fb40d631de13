@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Trading specific styles */
  .profit-text {
    @apply text-profit font-semibold;
  }
  
  .loss-text {
    @apply text-loss font-semibold;
  }
  
  .neutral-text {
    @apply text-muted-foreground;
  }
  
  .price-up {
    @apply text-profit bg-profit/10 px-2 py-1 rounded;
  }
  
  .price-down {
    @apply text-loss bg-loss/10 px-2 py-1 rounded;
  }
  
  .price-neutral {
    @apply text-muted-foreground bg-muted px-2 py-1 rounded;
  }
  
  /* Chart styles */
  .chart-container {
    @apply w-full h-full bg-card rounded-lg border p-4;
  }
  
  /* Table styles */
  .data-table {
    @apply w-full border-collapse border border-border;
  }
  
  .data-table th {
    @apply bg-muted px-4 py-2 text-left font-semibold border-b border-border;
  }
  
  .data-table td {
    @apply px-4 py-2 border-b border-border;
  }
  
  .data-table tr:hover {
    @apply bg-muted/50;
  }
  
  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
  }
  
  /* Form styles */
  .form-field {
    @apply space-y-2;
  }
  
  .form-label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }
  
  .form-input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .form-error {
    @apply text-sm text-destructive;
  }
  
  /* Dashboard grid */
  .dashboard-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }
  
  .dashboard-card {
    @apply bg-card border rounded-lg p-6 shadow-sm;
  }
  
  /* Trading interface */
  .trading-panel {
    @apply bg-card border rounded-lg p-4 space-y-4;
  }
  
  .order-book {
    @apply font-mono text-sm;
  }
  
  .order-book-bid {
    @apply text-profit;
  }
  
  .order-book-ask {
    @apply text-loss;
  }
  
  /* Responsive utilities */
  .mobile-hidden {
    @apply hidden md:block;
  }
  
  .mobile-only {
    @apply block md:hidden;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
