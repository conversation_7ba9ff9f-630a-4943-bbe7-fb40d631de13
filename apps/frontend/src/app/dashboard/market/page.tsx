'use client';

import { useEffect, useState } from 'react';
import { Activity, Wifi, WifiOff, Search } from 'lucide-react';
import { useWebSocket } from '@/hooks/useWebSocket';
import { useMarketDataStore, useMarketDataSelectors } from '@/stores/marketDataStore';
import { InstrumentSearch } from '@/components/market/InstrumentSearch';
import { PriceDisplay } from '@/components/market/PriceDisplay';
import { Watchlist } from '@/components/market/Watchlist';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export default function MarketDataPage() {
  const [selectedInstrumentKey, setSelectedInstrumentKey] = useState<string | null>(null);
  
  const { status, subscribe, unsubscribe } = useWebSocket();
  const { 
    selectedInstruments, 
    clearSelection,
    setConnectionStatus,
    error,
    clearError,
  } = useMarketDataStore();
  
  const { getInstrument, getTick, getSelectedInstrumentsData } = useMarketDataSelectors();

  // Update connection status in store
  useEffect(() => {
    setConnectionStatus(status.connected);
  }, [status.connected, setConnectionStatus]);

  // Subscribe to selected instruments
  useEffect(() => {
    if (status.connected && selectedInstruments.length > 0) {
      subscribe(selectedInstruments);
    }
  }, [status.connected, selectedInstruments, subscribe]);

  const selectedInstrument = selectedInstrumentKey ? getInstrument(selectedInstrumentKey) : null;
  const selectedTick = selectedInstrumentKey ? getTick(selectedInstrumentKey) : undefined;
  const selectedInstrumentsData = getSelectedInstrumentsData();

  const handleInstrumentSelect = (instrument: any) => {
    setSelectedInstrumentKey(instrument.instrument_key);
    if (status.connected) {
      subscribe([instrument.instrument_key]);
    }
  };

  const handleRemoveFromSelection = (instrumentKey: string) => {
    if (status.connected) {
      unsubscribe([instrumentKey]);
    }
    // Remove from store selection would be handled by the store
  };

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Market Data</h1>
          <p className="text-muted-foreground">
            Real-time market data and instrument tracking
          </p>
        </div>
        
        {/* Connection Status */}
        <div className="flex items-center space-x-2">
          <div className={cn(
            "flex items-center space-x-2 px-3 py-2 rounded-lg",
            status.connected 
              ? "bg-profit/10 text-profit" 
              : "bg-destructive/10 text-destructive"
          )}>
            {status.connected ? (
              <Wifi className="h-4 w-4" />
            ) : (
              <WifiOff className="h-4 w-4" />
            )}
            <span className="text-sm font-medium">
              {status.connected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {(error || status.error) && (
        <div className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-lg flex items-center justify-between">
          <span>{error || status.error}</span>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={clearError}
          >
            Dismiss
          </Button>
        </div>
      )}

      {/* Search Bar */}
      <div className="max-w-2xl">
        <InstrumentSearch 
          onSelect={handleInstrumentSelect}
          placeholder="Search for stocks, ETFs, indices..."
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Selected Instrument Detail */}
          {selectedInstrument && (
            <div className="bg-card border border-border rounded-lg p-6">
              <PriceDisplay
                instrument={selectedInstrument}
                tick={selectedTick}
                size="lg"
                showVolume={true}
              />
            </div>
          )}

          {/* Selected Instruments Grid */}
          {selectedInstrumentsData.length > 0 && (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Selected Instruments</h2>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={clearSelection}
                >
                  Clear All
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {selectedInstrumentsData.map(({ instrument, tick }) => (
                  <div 
                    key={instrument!.instrument_key}
                    className="bg-card border border-border rounded-lg p-4 cursor-pointer hover:bg-accent transition-colors"
                    onClick={() => setSelectedInstrumentKey(instrument!.instrument_key)}
                  >
                    <PriceDisplay
                      instrument={instrument!}
                      tick={tick}
                      size="md"
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Market Overview */}
          <div className="bg-card border border-border rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Market Overview</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-profit">+1.2%</div>
                <div className="text-sm text-muted-foreground">NIFTY 50</div>
              </div>
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-loss">-0.8%</div>
                <div className="text-sm text-muted-foreground">SENSEX</div>
              </div>
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-profit">+0.5%</div>
                <div className="text-sm text-muted-foreground">BANK NIFTY</div>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Watchlist */}
          <div className="bg-card border border-border rounded-lg p-6">
            <Watchlist />
          </div>

          {/* Market Status */}
          <div className="bg-card border border-border rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Market Status</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">NSE</span>
                <span className="text-sm text-profit">Open</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">BSE</span>
                <span className="text-sm text-profit">Open</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">MCX</span>
                <span className="text-sm text-muted-foreground">Closed</span>
              </div>
            </div>
          </div>

          {/* Connection Info */}
          <div className="bg-card border border-border rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Connection Info</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Status:</span>
                <span className={status.connected ? 'text-profit' : 'text-destructive'}>
                  {status.connected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Subscriptions:</span>
                <span>{selectedInstruments.length}</span>
              </div>
              {status.lastMessage && (
                <div className="flex justify-between">
                  <span>Last Update:</span>
                  <span>{new Date(status.lastMessage.timestamp).toLocaleTimeString()}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
