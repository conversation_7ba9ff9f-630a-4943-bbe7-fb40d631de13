{"name": "algorithmic-trading-platform", "version": "1.0.0", "description": "Professional algorithmic trading platform", "private": true, "packageManager": "npm@9.8.1", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "clean": "turbo run clean", "setup": "bash scripts/setup.sh", "verify": "node scripts/simple-verify.js", "verify:bash": "bash scripts/verify-setup.sh", "fix-permissions": "node scripts/fix-permissions.js", "db:migrate": "cd apps/backend && npm run db:migrate", "db:seed": "cd apps/backend && npm run db:seed", "db:reset": "cd apps/backend && npm run db:reset", "instruments:sync": "cd apps/backend && npm run instruments:sync", "docker:up": "docker-compose up -d postgres influxdb redis", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "health": "curl -s http://localhost:3001/api/health | jq ."}, "devDependencies": {"turbo": "^1.10.0", "@types/node": "^20.0.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0"}}