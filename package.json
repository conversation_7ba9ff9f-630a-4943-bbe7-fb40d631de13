{"name": "algorithmic-trading-platform", "version": "1.0.0", "description": "Professional algorithmic trading platform", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "clean": "turbo run clean", "db:migrate": "cd apps/backend && npm run db:migrate", "db:seed": "cd apps/backend && npm run db:seed"}, "devDependencies": {"turbo": "^1.10.0", "@types/node": "^20.0.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}