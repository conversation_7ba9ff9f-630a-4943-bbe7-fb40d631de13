version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: trading-postgres
    environment:
      POSTGRES_DB: trading_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./apps/backend/src/database/migrations:/docker-entrypoint-initdb.d
    networks:
      - trading-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # InfluxDB for time-series data
  influxdb:
    image: influxdb:2.7-alpine
    container_name: trading-influxdb
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: admin
      DOCKER_INFLUXDB_INIT_PASSWORD: password123
      DOCKER_INFLUXDB_INIT_ORG: trading-org
      DOCKER_INFLUXDB_INIT_BUCKET: market-data
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: trading-token-123456789
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    networks:
      - trading-network
    healthcheck:
      test: ["CMD", "influx", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: trading-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - trading-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server --appendonly yes

  # Backend API (development)
  backend:
    build:
      context: .
      dockerfile: apps/backend/Dockerfile
    container_name: trading-backend
    environment:
      NODE_ENV: development
      DATABASE_URL: ********************************************/trading_platform
      INFLUXDB_URL: http://influxdb:8086
      INFLUXDB_TOKEN: trading-token-123456789
      INFLUXDB_ORG: trading-org
      INFLUXDB_BUCKET: market-data
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-this
      PORT: 3001
    ports:
      - "3001:3001"
    volumes:
      - ./apps/backend:/app
      - /app/node_modules
    networks:
      - trading-network
    depends_on:
      postgres:
        condition: service_healthy
      influxdb:
        condition: service_healthy
      redis:
        condition: service_healthy
    profiles:
      - dev

  # Frontend (development)
  frontend:
    build:
      context: .
      dockerfile: apps/frontend/Dockerfile
    container_name: trading-frontend
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:3001
      NEXT_PUBLIC_WS_URL: ws://localhost:3001
    ports:
      - "3000:3000"
    volumes:
      - ./apps/frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - trading-network
    depends_on:
      - backend
    profiles:
      - dev

volumes:
  postgres_data:
  influxdb_data:
  influxdb_config:
  redis_data:

networks:
  trading-network:
    driver: bridge
