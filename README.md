# Algorithmic Trading Platform

A comprehensive, production-ready algorithmic trading platform built with modern technologies.

## 🏗️ Architecture

- **Frontend**: Next.js 14 with <PERSON><PERSON>, shadcn/ui, <PERSON>ust<PERSON>, TanStack Query
- **Backend**: Fastify with TypeScript
- **Databases**: PostgreSQL (relational data), InfluxDB (time-series), Redis (cache)
- **Authentication**: JWT with bcrypt password hashing
- **Real-time**: WebSockets for live data streaming
- **Deployment**: Docker containers with docker-compose

## 🚀 Features

- **Real-time Market Data**: Live price feeds and charts
- **Strategy Builder**: Visual strategy creation and backtesting
- **Portfolio Management**: Risk assessment and position tracking
- **Order Management**: Advanced order types and execution
- **Analytics Dashboard**: Performance metrics and reporting
- **Multi-user Support**: Role-based access control
- **API Integration**: Support for multiple market data providers

## 📁 Project Structure

```
├── apps/
│   ├── frontend/          # Next.js application
│   └── backend/           # Fastify API server
├── packages/
│   ├── shared/            # Shared types and utilities
│   ├── database/          # Database schemas and migrations
│   └── ui/                # Shared UI components
├── docker/                # Docker configurations
└── docs/                  # Documentation
```

## 🛠️ Development Setup

### Prerequisites

- Node.js 18+
- Docker and Docker Compose
- PostgreSQL 15+
- InfluxDB 2.0+
- Redis 7+

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd algorithmic-trading-platform
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp apps/backend/.env.example apps/backend/.env
cp apps/frontend/.env.example apps/frontend/.env.local
```

4. Start the development environment:
```bash
# Start databases
docker-compose up -d postgres influxdb redis

# Wait for databases to be ready (about 30 seconds)
sleep 30

# Run database migrations and seed data
npm run db:migrate
npm run db:seed

# Start development servers
npm run dev
```

5. Access the application:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - API Documentation: http://localhost:3001/docs

6. Login with demo accounts:
   - Admin: <EMAIL> / admin123
   - Trader: <EMAIL> / trader123

### Environment Variables

#### Backend (.env)
```
DATABASE_URL=postgresql://user:password@localhost:5432/trading_platform
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-influxdb-token
INFLUXDB_ORG=trading-org
INFLUXDB_BUCKET=market-data
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-super-secret-jwt-key
BCRYPT_ROUNDS=12
PORT=3001
```

#### Frontend (.env.local)
```
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001
```

## 🧪 Testing

```bash
npm run test          # Run all tests
npm run test:unit     # Unit tests only
npm run test:e2e      # End-to-end tests
```

## 📦 Deployment

```bash
npm run build         # Build all applications
docker-compose -f docker-compose.prod.yml up -d
```

## 📚 API Documentation

API documentation is available at `http://localhost:3001/docs` when running in development mode.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
