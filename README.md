# Algorithmic Trading Platform

A comprehensive, production-ready algorithmic trading platform built with modern technologies.

## 🏗️ Architecture

- **Frontend**: Next.js 14 with <PERSON><PERSON>, shadcn/ui, <PERSON>ust<PERSON>, TanStack Query
- **Backend**: Fastify with TypeScript
- **Databases**: PostgreSQL (relational data), InfluxDB (time-series), Redis (cache)
- **Authentication**: JWT with bcrypt password hashing
- **Real-time**: WebSockets for live data streaming
- **Deployment**: Docker containers with docker-compose

## 🚀 Features

- **Real-time Market Data**: Live price feeds via Upstox WebSocket API
- **Instrument Management**: Search, track, and manage trading instruments
- **Watchlists**: Create and manage custom instrument watchlists
- **Strategy Builder**: Visual strategy creation and backtesting
- **Portfolio Management**: Risk assessment and position tracking
- **Order Management**: Advanced order types and execution
- **Analytics Dashboard**: Performance metrics and reporting
- **Multi-user Support**: Role-based access control
- **Market Data Integration**: Upstox API integration with real-time streaming
- **Time-series Storage**: InfluxDB for historical market data analysis
- **Comprehensive Monitoring**: Health checks and system status monitoring

## 📁 Project Structure

```
├── apps/
│   ├── frontend/          # Next.js application
│   └── backend/           # Fastify API server
├── packages/
│   ├── shared/            # Shared types and utilities
│   ├── database/          # Database schemas and migrations
│   └── ui/                # Shared UI components
├── docker/                # Docker configurations
└── docs/                  # Documentation
```

## 🛠️ Development Setup

### Prerequisites

- **Node.js 18.0.0+** (LTS recommended)
- **npm 9.0.0+** (comes with Node.js)
- Docker and Docker Compose
- PostgreSQL 15+
- InfluxDB 2.0+
- Redis 7+
- **Upstox Developer Account** (for real-time market data)

> **Note**: The project uses npm as the package manager. Ensure you have the correct versions by checking `node --version` and `npm --version`.

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd algorithmic-trading-platform
```

2. Install dependencies:
```bash
npm install
```

3. **Set up Upstox API credentials** (Required for market data):
   - Register at [Upstox Developer Console](https://developer.upstox.com/)
   - Create a new app and obtain your API credentials
   - Note down your API Key, API Secret, and Access Token

4. Set up environment variables:
```bash
cp apps/backend/.env.example apps/backend/.env
cp apps/frontend/.env.example apps/frontend/.env.local
```

5. **Configure Upstox credentials** in `apps/backend/.env`:
```bash
UPSTOX_API_KEY=your-upstox-api-key
UPSTOX_API_SECRET=your-upstox-api-secret
UPSTOX_ACCESS_TOKEN=your-upstox-access-token
```

6. Start the development environment:
```bash
# Start databases
docker-compose up -d postgres influxdb redis

# Wait for databases to be ready (about 30 seconds)
sleep 30

# Run database migrations and seed data
npm run db:migrate
npm run db:seed

# Start development servers
npm run dev
```

7. **Sync instrument data** (Required for market data functionality):
```bash
# Login as admin and sync instruments from Upstox
curl -X POST http://localhost:3001/api/instruments/sync \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN"
```

8. Access the application:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - API Documentation: http://localhost:3001/docs
   - **Market Data Dashboard**: http://localhost:3000/dashboard/market

9. Login with demo accounts:
   - Admin: <EMAIL> / admin123
   - Trader: <EMAIL> / trader123

### Environment Variables

#### Backend (.env)
```
DATABASE_URL=postgresql://user:password@localhost:5432/trading_platform
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-influxdb-token
INFLUXDB_ORG=trading-org
INFLUXDB_BUCKET=market-data
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-super-secret-jwt-key
BCRYPT_ROUNDS=12
PORT=3001

# Upstox API Configuration
UPSTOX_API_KEY=your-upstox-api-key
UPSTOX_API_SECRET=your-upstox-api-secret
UPSTOX_REDIRECT_URI=http://localhost:3001/api/upstox/callback
UPSTOX_ACCESS_TOKEN=your-upstox-access-token
```

#### Frontend (.env.local)
```
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001
```

## 📈 Upstox Market Data Integration

### Features

- **Real-time Market Data**: Live price streaming via Upstox WebSocket API
- **Instrument Search**: Search across 40,000+ instruments from NSE, BSE, MCX
- **Watchlists**: Create and manage custom instrument watchlists
- **Historical Data**: Time-series data storage in InfluxDB
- **Market Monitoring**: System health checks and connection status

### Getting Started with Market Data

1. **Obtain Upstox API Credentials**:
   - Visit [Upstox Developer Console](https://developer.upstox.com/)
   - Create a developer account
   - Create a new app to get API Key and Secret
   - Generate an access token for your app

2. **Configure Environment Variables**:
   ```bash
   UPSTOX_API_KEY=your-api-key
   UPSTOX_API_SECRET=your-api-secret
   UPSTOX_ACCESS_TOKEN=your-access-token
   UPSTOX_REDIRECT_URI=http://localhost:3001/api/upstox/callback
   ```

3. **Sync Instrument Data**:
   ```bash
   # After starting the application, sync instruments (Admin only)
   curl -X POST http://localhost:3001/api/instruments/sync \
     -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
   ```

4. **Access Market Data Dashboard**:
   - Navigate to `/dashboard/market`
   - Search for instruments (e.g., "RELIANCE", "NIFTY")
   - Create watchlists and monitor real-time prices

### API Endpoints

- `GET /api/instruments/search?q=RELIANCE` - Search instruments
- `POST /api/market-data/subscribe` - Subscribe to real-time data
- `WS /ws/market-data` - WebSocket for live price feeds
- `GET /api/health` - System health including market data status

### Troubleshooting

- **No market data**: Ensure Upstox credentials are valid and market is open
- **WebSocket errors**: Check network connectivity and rate limits
- **Missing instruments**: Run the instrument sync endpoint
- **Authentication errors**: Verify Upstox access token is not expired

For detailed integration documentation, see [docs/UPSTOX_INTEGRATION.md](docs/UPSTOX_INTEGRATION.md)

## 🔧 Troubleshooting

### Package Manager Issues

If you encounter "package manager not specified" error:

```bash
# Check Node.js and npm versions
node --version  # Should be 18.0.0+
npm --version   # Should be 9.0.0+

# Run the setup script (automatically fixes package manager config)
npm run setup

# Or manually add to package.json
"packageManager": "npm@9.8.1"
```

### Common Issues

- **Node.js version**: Use Node.js 18+ (check with `node --version`)
- **npm version**: Use npm 9+ (update with `npm install -g npm@latest`)
- **Permission denied**: Use `npm run verify` instead of `./scripts/verify-setup.sh`
- **Docker issues**: Ensure Docker is running (`docker ps`)
- **Port conflicts**: Check if ports 3000, 3001, 5432, 8086, 6379 are available
- **Upstox credentials**: Verify API keys are correctly set in `.env`

### Script Permission Issues

If you get "Permission denied" errors:

```bash
# Use Node.js scripts (recommended)
npm run verify
npm run setup

# Or fix permissions manually
chmod +x scripts/*.sh

# Or run with bash explicitly
bash scripts/setup.sh
```

For detailed troubleshooting, see:
- [Permission Troubleshooting](docs/PERMISSION_TROUBLESHOOTING.md)
- [Package Manager Setup](docs/PACKAGE_MANAGER_SETUP.md)

## 🧪 Testing

```bash
npm run test          # Run all tests
npm run test:unit     # Unit tests only
npm run test:e2e      # End-to-end tests
```

## 📦 Deployment

```bash
npm run build         # Build all applications
docker-compose -f docker-compose.prod.yml up -d
```

## 📚 Documentation

### Quick Start
- **[Development Guide](docs/DEVELOPMENT_GUIDE.md)** - Complete setup and development instructions
- **[Upstox Integration](docs/UPSTOX_INTEGRATION.md)** - Market data integration guide

### API & Architecture
- [API Documentation](http://localhost:3001/docs) - Interactive Swagger documentation
- [Architecture Overview](docs/ARCHITECTURE.md) - System design and components
- [Database Schema](docs/DATABASE.md) - Database structure and relationships

### Deployment & Operations
- [Deployment Guide](docs/DEPLOYMENT_GUIDE.md) - Production deployment instructions
- [Docker Configuration](docker-compose.yml) - Development environment setup
- [Environment Variables](.env.docker.example) - Configuration template

### Scripts & Automation
- [Setup Script](scripts/setup.sh) - Automated development setup
- [Commands Reference](docs/COMMANDS_REFERENCE.md) - Complete command reference
- [Package Manager Setup](docs/PACKAGE_MANAGER_SETUP.md) - Package manager configuration
- [Permission Troubleshooting](docs/PERMISSION_TROUBLESHOOTING.md) - Fix script permission issues
- [Package Scripts](package.json) - Available npm commands

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
